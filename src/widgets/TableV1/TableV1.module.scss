.Table {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loaderContainer {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  z-index: 999;
}

.Head {
  width: 100%;
}

.Body {
  width: 100%;
  height: 100%;
  padding-top: 0.2rem;
}

.Paper {
  box-shadow: none !important;
  height: 100%;
  position: relative;

  th {
    background-color: var(--background-color-primary);
    border-bottom: solid 1px var(--row-color-gray);
    border-right: solid 2px var(--gray-background) !important;
  }

  td {
    position: relative;
    border-right: solid 1px var(--row-color-gray);
  }
}

.Table tfoot td {
  font-weight: bold;
  color: var(--text-color);
}
.Table th {
  font-weight: bold;
}

// Скрываем поля поиска (фильтров) для колонок, где поиск отключен
.Table th[class*='TableFilterCell']:has([disabled]) > * {
  display: none !important;
}

// Подсветка строк при наведении
.Table tbody tr:hover {
  background-color: var(--gray-background) !important;
}
