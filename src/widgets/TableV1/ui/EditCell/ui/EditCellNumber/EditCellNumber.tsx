import { TableInlineCellEditing as TableInlineCellEditingBase } from '@devexpress/dx-react-grid'
import { Table } from '@devexpress/dx-react-grid-material-ui'
import { IconButton, TextFieldProps, Tooltip } from '@mui/material'
import { useRef, useState } from 'react'
import { useOnClickOutside } from 'shared/lib/useOnClickOutside'
import { Icon, TextField } from 'shared/ui'
import { IBaseRowData } from 'widgets/TableV1'
import { IEditCellColumn } from 'widgets/TableV1/ui'

import cls from './EditCellNumber.module.scss'

export interface EditCellNumberProps<RowData extends IBaseRowData> extends TableInlineCellEditingBase.CellProps {
  column: IEditCellColumn<RowData, 'number'>
  row: RowData
}

export const EditCellNumber = <RowData extends IBaseRowData>(props: EditCellNumberProps<RowData>) => {
  const { column, value, onBlur, row, tableColumn, tableRow, onValueChange } = props
  const { editing } = column
  const [number, setNumber] = useState(value ?? '')
  const initialNumber = useRef(value ?? '')
  const textFieldRef = useRef(null)

  const handleChangeNumber: TextFieldProps['onChange'] = (event) => {
    const value = event.target.value
    if (value.includes(',')) {
      return
    }
    setNumber(value)
  }

  const handleCloseEditor = () => {
    const changedNumber = number ? +number : NaN
    onValueChange(changedNumber)
    if (editing?.onAfterChange) {
      editing.onAfterChange(changedNumber, row)
    }
    onBlur()
  }

  const handleKeyDown: TextFieldProps['onKeyDown'] = (event) => {
    if (event.code === 'Escape') {
      setNumber(initialNumber.current)
      handleCloseEditor()
    }
    if (event.code === 'Enter') {
      return handleCloseEditor()
    }
  }

  const handleClearNumber = () => setNumber('')

  useOnClickOutside(textFieldRef, () => handleCloseEditor())

  return (
    <Table.Cell column={column} row={row} tableColumn={tableColumn} tableRow={tableRow} value={value}>
      <div className={cls.editCell} ref={textFieldRef}>
        <TextField
          autoFocus
          type='number'
          value={number}
          className={cls.textEdit}
          onChange={handleChangeNumber}
          inputProps={{
            min: editing?.numberOption?.min,
          }}
          numberOption={editing?.numberOption}
          onKeyDown={handleKeyDown}
        />
        {editing?.showClearButton && number.length > 0 && (
          <Tooltip title='Удалить'>
            <IconButton onClick={handleClearNumber} className={cls.closeIcon}>
              <Icon name='trash' width={11} />
            </IconButton>
          </Tooltip>
        )}
      </div>
    </Table.Cell>
  )
}
