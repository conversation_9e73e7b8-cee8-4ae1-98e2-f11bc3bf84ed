import { TableInlineCellEditing as TableInlineCellEditingBase } from '@devexpress/dx-react-grid'
import { Table } from '@devexpress/dx-react-grid-material-ui'
import { IconButton, Tooltip } from '@mui/material'
import { format, isValid, startOfDay } from 'date-fns'
import { KeyboardEvent, useMemo, useRef, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { useOnClickOutside } from 'shared/lib/useOnClickOutside'
import { Icon } from 'shared/ui'
import { DatePicker } from 'shared/ui/DatePicker/DatePicker.tsx'
import { IBaseRowData } from 'widgets/TableV1'
import { IEditCellColumn } from 'widgets/TableV1/ui'

import clsEditCell from '../../EditCell.module.scss'
import cls from './EditCellDate.module.scss'

export interface EditCellDateProps<RowData extends IBaseRowData> extends TableInlineCellEditingBase.CellProps {
  column: IEditCellColumn<RowData, 'date'>
  row: RowData
}

const DELETE_ICON_WIDTH = 20

export const EditCellDate = <RowData extends IBaseRowData>(props: EditCellDateProps<RowData>) => {
  const { column, value, onBlur, row, tableColumn, tableRow, onValueChange } = props
  const { editing } = column
  const [isOpen, setIsOpen] = useState(false)
  const initialDate = useRef(value ? new Date(value) : null)
  const [date, setDate] = useState<Date | null>(() => (value ? new Date(value) : null))
  const datePickerRef = useRef(null)

  const error = useMemo(() => {
    if (editing?.onValid) {
      return editing.onValid(date ? startOfDay(date) : null, row)
    }
  }, [editing?.onValid, date])

  const onCloseEditor = () => {
    if (!isOpen) {
      let res: string | null = null
      let updatedDate = initialDate.current
      if (date && isValid(date)) {
        if ((editing?.onValid && !editing.onValid(startOfDay(date), row)) || !editing?.onValid) {
          updatedDate = date
          res = format(updatedDate, 'yyyy-MM-dd')
        }
      } else if (date === null) {
        updatedDate = null
        res = null
      } else {
        res = null
      }
      onValueChange(res)
      onBlur()
      if (editing?.onAfterChange) {
        editing.onAfterChange(updatedDate, row)
      }
    }
  }

  useOnClickOutside(datePickerRef, () => onCloseEditor())

  const handleKeyDown = (event: KeyboardEvent<HTMLDivElement>) => {
    if (event.code === 'Escape') {
      return onBlur()
    }
    if (event.code === 'Enter') {
      return onCloseEditor()
    }
  }

  return (
    <Table.Cell column={column} row={row} tableColumn={tableColumn} tableRow={tableRow} value={value}>
      <div
        ref={datePickerRef}
        style={{ width: Number(column.width) - DELETE_ICON_WIDTH }}
        className={classNames(clsEditCell.editCell, {}, [cls.editCellDatePicker])}
      >
        <DatePicker
          focused
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          value={date as Date}
          setValue={setDate}
          className={cls.datePicker}
          disablePast={editing?.disablePast}
          onKeyDown={handleKeyDown}
          error={error}
          showErrorMsg={editing?.showErrorMessage}
          handleAllDates
        />
        {editing?.showClearButton && date && (
          <Tooltip title='Удалить дату'>
            <IconButton onClick={() => setDate(null)} className={clsEditCell.closeIcon}>
              <Icon name='trash' width={11} />
            </IconButton>
          </Tooltip>
        )}
      </div>
    </Table.Cell>
  )
}
