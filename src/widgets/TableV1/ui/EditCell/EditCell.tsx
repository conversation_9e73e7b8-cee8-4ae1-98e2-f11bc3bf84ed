import { TableInlineCellEditing as TableInlineCellEditingBase } from '@devexpress/dx-react-grid'
import { useLayoutEffect } from 'react'
import { IBaseRowData, IColumn, TColumnEditingType } from 'widgets/TableV1'

import { EditCellDate, EditCellNumber, EditCellSwitch } from './ui'

export interface IEditCellColumn<RowData extends IBaseRowData, EditType extends TColumnEditingType>
  extends Omit<IColumn<RowData, keyof RowData, EditType>, 'width'> {
  width?: number | string
}

interface EditCellProps<RowData extends IBaseRowData> extends TableInlineCellEditingBase.CellProps {
  column: IEditCellColumn<RowData, 'all'>
  row: RowData
}

export const EditCell = <RowData extends IBaseRowData>(props: EditCellProps<RowData>) => {
  const { column, row, onBlur } = props

  // Блокируем доступ к переходу в режим редактирования если не выполняются условия
  useLayoutEffect(() => {
    const isEditingEnabled =
      typeof column.editing?.enabled === 'function' ? column.editing.enabled(row) : column.editing?.enabled

    if (!isEditingEnabled) {
      onBlur()
    }
  }, [column.editing?.enabled, row, onBlur])

  // TODO: вынести в объект, где ключом является тип, а значением ссылка на компонент и решить проблему с приведением типов
  switch (column.editing?.type) {
    case 'date':
      return (
        <EditCellDate
          {...props}
          column={{
            ...column,
            editing: column.editing,
          }}
        />
      )
    case 'switch':
      return (
        <EditCellSwitch
          {...props}
          column={{
            ...column,
            editing: column.editing,
          }}
        />
      )
    case 'number':
      return (
        <EditCellNumber
          {...props}
          column={{
            ...column,
            editing: column.editing,
          }}
        />
      )
    default:
      return null
  }
}
