import { Table } from '@devexpress/dx-react-grid-material-ui'
import { CSSProperties, useMemo } from 'react'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { Switch } from 'shared/ui/Switch'
import { useContextMenu } from 'widgets/Table/hooks/useContextMenu.tsx'
import { IBaseRowData, IColumn } from 'widgets/TableV1'

import cls from './TableCell.module.scss'

export interface TableCellProps<RowData extends IBaseRowData> extends Table.DataCellProps {
  column: Omit<IColumn<RowData>, 'width'>
  editMode: boolean
  row: RowData
  value: RowData[keyof RowData]
}

export const TableCell = <RowData extends IBaseRowData>(props: TableCellProps<RowData>) => {
  const { editMode, ...tableCellProps } = props
  const { column, row, value } = tableCellProps
  const { stylesCell } = row
  const { contextMenuItem, handleContextMenu } = useContextMenu()

  const style = (stylesCell?.[column?.name] as CSSProperties) ?? {}

  const isEditingEnabled =
    typeof column.editing?.enabled === 'function' ? column.editing?.enabled(row) : (column.editing?.enabled ?? true)
  const editable = isEditingEnabled && editMode
  const rowSpan = column?.getCellRowSpan?.(row) ?? 1
  const defaultClassName = classNames(
    cls.cell,
    {
      [cls.editable]: editable,
      [cls.error]: row.cellsErrors?.includes(column.name),
    },
    [column?.getCellClassName?.(value)],
  )

  const renderedCellValue = useMemo(() => {
    if (column?.getCellRenderValue) {
      return column.getCellRenderValue(value, row)
    }

    return value
  }, [column, value])

  if (!rowSpan) return <></>

  if (column.editing?.type === 'switch') {
    return (
      <Table.Cell {...tableCellProps} style={{ ...style }} rowSpan={rowSpan}>
        <div className={classNames(`${cls.cell} ${cls.cellSwitch}`, { [cls.editable]: editable }, [])}>
          <Switch checked={!!value} disabled={!editable} />
        </div>
      </Table.Cell>
    )
  }

  return (
    <Table.Cell
      rowSpan={rowSpan}
      {...tableCellProps}
      style={{ ...style, cursor: 'context-menu' }}
      onContextMenu={handleContextMenu}
      title={typeof renderedCellValue === 'string' ? renderedCellValue : undefined}
    >
      {column.render ? (
        column.render(value, row, defaultClassName)
      ) : (
        <div className={defaultClassName}>{renderedCellValue}</div>
      )}
      {contextMenuItem(renderedCellValue)}
    </Table.Cell>
  )
}
