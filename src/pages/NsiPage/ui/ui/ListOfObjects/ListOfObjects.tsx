import { ISaveRegistry } from 'entities/api/nsiManager.entities.ts'
import {
  getIcon,
  IRowDepartmentsVer,
  IRowListOfObject,
  KEY_TABLE,
  ListOfObjectsProps,
  THierarchyType,
} from 'entities/pages/nsiPage.entities.ts'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import {
  getIsEdit,
  prepareDataBeforeSave,
  prepareDepartmentsVer,
  prepareRows,
} from 'pages/NsiPage/ui/ui/ListOfObjects/lib/helpers.ts'
import { useEffect, useState } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { prepareFlatData } from 'shared/lib/prepareData'
import { Icon } from 'shared/ui/Icon'
import { useStore } from 'stores/useStore.ts'
import { Table } from 'widgets/Table'

import cls from './ListOfObjects.module.scss'
import { HeaderComponents } from './ui/HeaderComponents'
import { ModalActionLog } from './ui/ModalActionLog'
import { ModalChangeLog } from './ui/ModalChangeLog'
import { ModalUploadStations } from './ui/ModalUploadStations'

export const ListOfObjects = observer((props: ListOfObjectsProps) => {
  const { height, userDetail } = props
  const { nsiStore } = useStore()
  const {
    initListOfObjects,
    registry,
    startSk11,
    isUpdateNsi,
    infoUploadStations,
    saveRegistry,
    isLoadingNsi,
    activeDepartments,
    hierarchyType,
  } = nsiStore
  const [isModalUploadStations, setIsModalUploadStations] = useState(false)
  const [isShowModalActionLog, setIsShowModalActionLog] = useState(false)
  const [isShowModalChangeLog, setIsShowModalChangeLog] = useState(false)
  const [isShowNotValid, setIsShowNotValid] = useState(true)
  const [rows, setRows] = useState<IRowListOfObject[]>([])
  const [rowsOrigin, setRowsOrigin] = useState<IRowListOfObject[]>([])
  const editMode = userDetail.roles
    .map((el) => el.role)
    .some((el: string) => {
      return [ROLES.TECH_ADMIN_NSI].some((item) => item === el)
    })
  const initExpandedRowIds = JSON.parse(localStorage.getItem(`expanded-${KEY_TABLE}`) as string) ?? []
  const [expandedRowIds, setExpandedRowIds] = useState<string[]>([])
  const departments = prepareDepartmentsVer(activeDepartments as IRowDepartmentsVer[])
  const columns = [
    {
      name: 'fullPath',
      title: 'Наименование',
      width: 500,
      editingEnabled: false,
      render: (_: unknown, row: IRowListOfObject) => {
        return (
          <>
            <div
              className={classNames(
                cls.icon,
                {
                  [cls.BlueIcon]:
                    row.type === 'RGU' ||
                    row.type === 'GENERATOR' ||
                    row.type === 'PLANT' ||
                    row.type === 'RGU_RELATION',
                },
                [],
              )}
            >
              <Icon width={20} name={getIcon(row.type)} />
            </div>
            <>{row.name}</>
          </>
        )
      },
    },
    {
      name: 'werDepartments',
      title: 'ДЦ (ВЭР)',
      width: 290,
      editType: 'groups',
      editingEnabled: true,
      chooseItems: departments,
      chooseItemsOptions: { rule: 'nsi', isGroups: true },
      render: (value: { label: string }[]) => {
        return (
          <div className={classNames(cls.InputWrapper, {}, [])}>
            {value?.length > 0 &&
              value?.map((item, index) => {
                return (
                  <div className={classNames(cls.ChooseContainer, {}, [])} key={`autocomplete-tag-${index}`}>
                    {item?.label}
                  </div>
                )
              })}
          </div>
        )
      },
      customSearching: (value: { label: string }[], filter: { value: string }) => {
        const arr = value.map((el) => el?.label ?? '').join(' ')

        return arr.toUpperCase().includes(filter.value.toUpperCase())
      },
      canClearCell: true,
    },
    {
      name: 'planDepartments',
      title: 'ДЦ (план)',
      width: 270,
      editType: 'groups',
      editingEnabled: true,
      chooseItems: departments,
      chooseItemsOptions: { isGroups: true },
      render: (value: { label: string }[]) => {
        return (
          <div className={classNames(cls.InputWrapper, {}, [])}>
            {value?.length > 0 &&
              value?.map((item, index) => {
                return (
                  <div className={classNames(cls.ChooseContainer, {}, [])} key={`autocomplete-tag-${index}`}>
                    {item.label}
                  </div>
                )
              })}
          </div>
        )
      },
      customSearching: (value: { label: string }[], filter: { value: string }) => {
        const arr = value.map((el) => el?.label ?? '').join(' ')

        return arr.toUpperCase().includes(filter.value.toUpperCase())
      },
      canClearCell: true,
    },
    {
      name: 'marketCalcModelId',
      title: 'ID Рыночной РМ',
      width: 200,
      editingEnabled: true,
      editType: 'text',
      canClearCell: true,
    },
    {
      name: 'startDate',
      title: 'Дата начала',
      width: 130,
      editingEnabled: true,
      editType: 'date',
      disablePast: true,
      customSorting: (a: string, b: string) => {
        const [aDay, aMonth, aYear] = a.split('.')
        const [bDay, bMonth, bYear] = b.split('.')
        const dateA = new Date(Number(aYear), Number(aMonth), Number(aDay))
        const dateB = new Date(Number(bYear), Number(bMonth), Number(bDay))

        return dateA < dateB ? -1 : 1
      },
    },
    {
      name: 'endDate',
      title: 'Дата окончания',
      width: 160,
      editingEnabled: true,
      editType: 'date',
      disablePast: true,
      customSorting: (a: string, b: string) => {
        const [aDay, aMonth, aYear] = a.split('.')
        const [bDay, bMonth, bYear] = b.split('.')
        const dateA = new Date(Number(aYear), Number(aMonth), Number(aDay))
        const dateB = new Date(Number(bYear), Number(bMonth), Number(bDay))

        return dateA < dateB ? -1 : 1
      },
      canClearCell: true,
    },
    { name: 'uid', title: 'UID', width: 300, editingEnabled: false },
  ]
  useEffect(() => () => nsiStore.changeHierarchyType('BY_RGU'), [])
  useEffect(() => {
    initListOfObjects(editMode, hierarchyType)

    return () => {
      nsiStore.reset()
    }
  }, [hierarchyType])
  useEffect(() => {
    setRows(registry as unknown as IRowListOfObject[])
    setRowsOrigin(registry as unknown as IRowListOfObject[])
    const res = prepareFlatData(registry as unknown as IRowListOfObject[])
      .map((el) => {
        if (el.name.toUpperCase().includes('ЦДУ')) {
          return el
        }

        return null
      })
      .filter((el) => el)
      .map((el) => el?.tabId)
    setExpandedRowIds(initExpandedRowIds?.length > 0 ? initExpandedRowIds : res)
  }, [isShowNotValid, registry])
  const finRows = !isShowNotValid ? rows : prepareRows(rows)
  const edits = getIsEdit(finRows)
  const updateIsEdit = (rows: IRowListOfObject[]): IRowListOfObject[] => {
    return rows.map((el) => {
      const children = el.children && el.children.length > 0 ? updateIsEdit(el.children) : []
      const obj = el
      delete obj.isEdit
      delete obj.tempEdits

      return { ...obj, children }
    })
  }
  const onSave = () => {
    const elements = prepareDataBeforeSave(rows)
    if (saveRegistry) {
      saveRegistry(elements as unknown as ISaveRegistry[], hierarchyType).then((isComplete: boolean) => {
        if (isComplete) {
          const res = [...updateIsEdit(rows)]
          setRows(res)
          setRowsOrigin(res)
        }
      })
    }
  }

  const getHeaderComponents = () => {
    return (
      <HeaderComponents
        handleModalUploadStations={() => {
          setIsModalUploadStations(true)
        }}
        handleModalUpdate={() => {
          if (startSk11) {
            startSk11()
          }
        }}
        handleModalActionLog={() => {
          setIsShowModalActionLog(true)
        }}
        handleModalChangeLog={() => {
          setIsShowModalChangeLog(true)
        }}
        isShowNotValid={isShowNotValid}
        setIsShowNotValid={setIsShowNotValid}
        isUpdateNsi={isUpdateNsi}
        edits={edits as unknown as string[]}
        resetData={() => {
          setRows([...rowsOrigin])
        }}
        onSave={onSave}
        editMode={editMode}
        hierarchyType={hierarchyType}
        setHierarchyType={(value: string) => nsiStore.changeHierarchyType(value as THierarchyType)}
        resetExpandedRowIds={() => {
          localStorage.removeItem(`expanded-${KEY_TABLE}`)
          setExpandedRowIds([])
        }}
      />
    )
  }

  return (
    <div className={cls.container}>
      <Table
        tableKey={KEY_TABLE}
        childKey='fullPath'
        loading={isLoadingNsi}
        headerComponents={getHeaderComponents()}
        initialData={rowsOrigin}
        rows={finRows}
        setRows={setRows}
        columns={columns}
        tableType='nsi'
        height={height}
        editMode={editMode}
        expandedRowIds={expandedRowIds}
        setExpandenRowIds={(e: string[]) => {
          localStorage.setItem(`expanded-${KEY_TABLE}`, JSON.stringify(e))
          setExpandedRowIds(e)
        }}
      />
      {isModalUploadStations && (
        <ModalUploadStations
          onClose={() => {
            setIsModalUploadStations(false)
          }}
          modelVersion={infoUploadStations.modelVersion as unknown as string}
          loadDate={infoUploadStations.loadDate}
          hierarchyType={hierarchyType}
          departments={departments as IRowDepartmentsVer[]}
        />
      )}
      {isShowModalActionLog && (
        <ModalActionLog
          onClose={() => {
            setIsShowModalActionLog(false)
          }}
        />
      )}
      {isShowModalChangeLog && (
        <ModalChangeLog
          onClose={() => {
            setIsShowModalChangeLog(false)
          }}
        />
      )}
    </div>
  )
})
