import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import Tooltip from '@mui/material/Tooltip'
import { format } from 'date-fns'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import { type Dispatch, type SetStateAction } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { AccessControl } from 'shared/ui/AccessControl'
import { Button } from 'shared/ui/Button'
import { CheckEditComponent } from 'shared/ui/CheckEditComponent'
import { Select } from 'shared/ui/Select'
import { Switch } from 'shared/ui/Switch'
import { useStore } from 'stores/useStore.ts'

import cls from './HeaderComponents.module.scss'

interface HeaderComponentsProps {
  className?: string
  isShowNotValid?: boolean
  editMode?: boolean
  edits?: string[]
  hierarchyType?: string
  setHierarchyType?: Dispatch<string>
  resetExpandedRowIds?: () => void
  isUpdateNsi?: boolean
  setIsShowNotValid?: Dispatch<SetStateAction<boolean>>
  handleModalUploadStations?: () => void
  handleModalUpdate?: () => void
  handleModalActionLog?: () => void
  handleModalChangeLog?: () => void
  resetData?: () => void
  onSave?: () => void
}

export const HeaderComponents = observer((props: HeaderComponentsProps) => {
  const {
    className,
    handleModalUploadStations,
    handleModalUpdate,
    handleModalActionLog,
    handleModalChangeLog,
    isShowNotValid,
    setIsShowNotValid,
    edits = [],
    resetData,
    onSave,
    editMode,
    hierarchyType,
    setHierarchyType,
    resetExpandedRowIds,
  } = props
  const { nsiStore } = useStore()
  const { statusTask } = nsiStore

  useHotkeys('ctrl+shift+s', () => onSave && edits.length > 0 && onSave())
  useHotkeys('ctrl+shift+x', () => resetData && edits.length > 0 && resetData())

  const hierarchyTypes = [
    { value: 'BY_GENERATOR', label: 'ГГ-РГЕ' },
    { value: 'BY_RGU', label: 'РГЕ-ГГ' },
  ]

  return (
    <CheckEditComponent isEdit={edits?.length !== 0}>
      <div className={classNames(cls.HeaderComponents, {}, className ? [className] : [])}>
        <div className={classNames(cls.Left, {}, [])}>
          <AccessControl rules={[ROLES.TECH_ADMIN_NSI]}>
            <Button
              onClick={handleModalUploadStations}
              className={classNames(cls.Action, {}, [])}
              variant='text'
              icon='leaf'
            >
              <div className={classNames(cls.ButtonLabel, {}, [])}>Загрузить станции</div>
            </Button>
            <Tooltip
              title={
                statusTask.status === 'IN_PROCESS' || nsiStore.isUpdateNsi
                  ? 'Идёт обновление НСИ'
                  : statusTask.status === 'DONE' && statusTask.updatedDate
                    ? `Обновлено ${format(
                        new Date(statusTask.updatedDate),
                        'dd.MM.yyy HH:mm:ss',
                      )}, версия модели: ${statusTask.id}`
                    : statusTask.status === 'FAILED'
                      ? `Ошибка обновления НСИ${statusTask.id ? `, активная версия модели: ${statusTask.id}` : null}`
                      : ''
              }
            >
              <div>
                <Button
                  disabled={statusTask.status === 'IN_PROCESS' || nsiStore.isUpdateNsi}
                  onClick={handleModalUpdate}
                  className={classNames(
                    cls.Action,
                    {
                      [cls.updateLoader]: statusTask.status === 'IN_PROCESS' || nsiStore.isUpdateNsi,
                      [cls.errorLoader]: statusTask.status === 'FAILED',
                    },
                    [],
                  )}
                  variant='text'
                  icon='update'
                >
                  <div className={classNames(cls.ButtonLabel, {}, [])}>Обновить НСИ</div>
                </Button>
              </div>
            </Tooltip>
          </AccessControl>
          <AccessControl
            rules={[ROLES.ADMIN, ROLES.TECH_ADMIN_NSI, ROLES.TECHNOLOGIST, ROLES.TECH_ADMIN_CM, ROLES.GUEST]}
          >
            <Button
              onClick={handleModalActionLog}
              className={classNames(cls.Action, {}, [])}
              variant='text'
              icon='backTime'
            >
              <div className={classNames(cls.ButtonLabel, {}, [])}>История действий</div>
            </Button>
            <Button
              onClick={handleModalChangeLog}
              className={classNames(cls.Action, {}, [])}
              variant='text'
              icon='journal'
            >
              <div className={classNames(cls.ButtonLabel, {}, [])}>Журнал изменений</div>
            </Button>
            <div className={cls.switchContainer}>
              <Switch
                label='Все объекты'
                checked={isShowNotValid}
                disabled={edits && edits.length !== 0}
                onChange={(_, value) => {
                  setIsShowNotValid && setIsShowNotValid(value)
                }}
              />
            </div>
            <div className={cls.selectContainer}>
              <Select
                disabled={edits.length !== 0}
                items={hierarchyTypes}
                value={hierarchyType}
                onChange={(value) => {
                  resetExpandedRowIds && resetExpandedRowIds()
                  setHierarchyType && setHierarchyType(value)
                }}
              />
            </div>
          </AccessControl>
        </div>
        <div className={classNames(cls.Right, {}, [])}>
          {editMode && (
            <>
              {edits && edits.length > 0 && (
                <div className={classNames(cls.CheckContainer, {}, [])}>
                  <div className={classNames(cls.CheckIcon, {}, [])}>
                    <CheckCircleIcon />
                  </div>
                  <div>
                    <>Изменено: {edits.length}</>
                  </div>
                </div>
              )}
              <Button
                disabled={edits && edits.length === 0}
                className={classNames(cls.Action, {}, [])}
                variant='outlined'
                onClick={() => {
                  resetData && resetData()
                }}
              >
                Сбросить
              </Button>
              <Button
                disabled={edits && edits.length === 0}
                className={classNames(cls.Action, {}, [])}
                onClick={onSave}
              >
                Сохранить
              </Button>
            </>
          )}
        </div>
      </div>
    </CheckEditComponent>
  )
})
