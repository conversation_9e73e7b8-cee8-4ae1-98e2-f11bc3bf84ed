import { isAfter, isSameMonth, startOfMonth, subMonths } from 'date-fns'
import {
  IResPrepareDataBeforeSave,
  IRowDepartmentsVer,
  IRowListOfObject,
  TDepartemntsVer,
} from 'entities/pages/nsiPage.entities.ts'
import { sortAlphabet } from 'shared/lib/sortAlphabet'

export const prepareDepartmentsVer = (arr: IRowDepartmentsVer[]) => {
  const [cdu, ...otherDepartments] = arr
  const odu: TDepartemntsVer = []
  const rdu: TDepartemntsVer = []
  const other: TDepartemntsVer = []

  otherDepartments.forEach((el) => {
    const isOdu = el.label.toUpperCase().includes('ОДУ')
    const isRdu = el.label.toUpperCase().includes('РДУ')

    if (isOdu) {
      odu.push(el)
    } else if (isRdu) {
      rdu.push(el)
    } else {
      other.push(el)
    }
  })

  const result = [
    { label: 'ЦДУ', children: [cdu] },
    { label: 'ОДУ', children: odu.sort(sortAlphabet) },
    { label: 'РДУ', children: rdu.sort(sortAlphabet) },
  ]

  if (other.length > 0) {
    result.push({ label: 'Прочие', children: other.sort(sortAlphabet) })
  }

  return result
}

export const prepareRows = (rows: IRowListOfObject[]): IRowListOfObject[] => {
  const res = rows.map((el) => {
    const children = el?.children ? prepareRows(el?.children) : []

    return {
      ...el,
      children,
    }
  })

  return res.filter((el) => el?.active && !(el?.type === 'DEPARTMENT' && !el?.children.length))
}

/**
 * Фильтрует объекты согласно расширенным правилам:
 * - Действующие станции (active = true)
 * - Станции с датой окончания (endDate) в предыдущем месяце
 * - Станции с датой начала (startDate) в будущем
 */
export const prepareRowsWithExtendedFilter = (rows: IRowListOfObject[]): IRowListOfObject[] => {
  const now = new Date()
  const previousMonth = startOfMonth(subMonths(now, 1))

  const res = rows.map((el) => {
    const children = el?.children ? prepareRowsWithExtendedFilter(el?.children) : []

    return {
      ...el,
      children,
    }
  })

  return res.filter((el) => {
    // Всегда показываем ЦДУ/ОДУ/РДУ, если у них есть дочерние элементы
    if (el?.type === 'DEPARTMENT') {
      return el?.children && el?.children.length > 0
    }

    // Для станций применяем расширенную фильтрацию
    if (el?.type === 'PLANT' || el?.type === 'RGU' || el?.type === 'GENERATOR' || el?.type === 'RGU_RELATION') {
      // 1. Действующие станции
      if (el?.active) {
        return true
      }

      // 2. Электростанции с датой окончания в предыдущем месяце
      if (el?.endDate) {
        const endDate = new Date(el.endDate)

        if (isSameMonth(endDate, previousMonth)) {
          return true
        }
      }

      // 3. Электростанции с датой начала в будущем
      if (el?.startDate) {
        const startDate = new Date(el.startDate)

        if (isAfter(startDate, now)) {
          return true
        }
      }

      return false
    }

    // Для остальных типов объектов используем стандартную логику
    return el?.active
  })
}

export const getIsEdit = (rows: IRowListOfObject[]) => {
  let res: IRowListOfObject[] = []
  rows.forEach((el) => {
    const children = el.children && el.children.length > 0 ? getIsEdit(el.children) : []
    if (el?.isEdit) {
      res.push(el)
    }
    if (children.length > 0) {
      res = [...res, ...children]
    }
  })

  return res
}

export const prepareSaveDate = (date: string) => {
  return date.split('.').reverse().join('-')
}

export const prepareDataBeforeSave = (arr: IRowListOfObject[]) => {
  let res: IResPrepareDataBeforeSave[] = []
  arr.forEach((el) => {
    if (el.isEdit) {
      const finObject = {
        type: el?.type ?? null,
        id: el?.id ?? null,
        startDate: el?.startDate?.length > 0 ? prepareSaveDate(el?.startDate) : null,
        endDate: el?.endDate?.length > 0 ? prepareSaveDate(el?.endDate) : null,
        planDepartmentIds:
          el?.planDepartments && el?.planDepartments?.length > 0 ? el?.planDepartments.map((el) => el.value) : null,
        marketCalcModelId: el?.marketCalcModelId ?? null,
        werDepartmentIds: el.werDepartments ? el.werDepartments.filter((el) => el).map((el) => el?.value) : [],
      }
      for (const [key, value] of Object.entries(finObject)) {
        if (value === null) {
          delete finObject[key as keyof IResPrepareDataBeforeSave]
        }
      }
      res.push(finObject)
    }
    const children = el?.children && el?.children?.length > 0 ? prepareDataBeforeSave(el?.children) : []
    if (children.length > 0) {
      res = [...res, ...children]
    }
  })

  return res
}
