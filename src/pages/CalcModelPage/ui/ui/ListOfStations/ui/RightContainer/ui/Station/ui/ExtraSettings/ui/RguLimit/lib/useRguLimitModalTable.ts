import { IParametersConfigsOutput } from 'entities/api/calcModelPage.entities.ts'
import {
  IToggleableRguRestrictionLimitParameter,
  IToggleableRguRestrictionLimitParameterValue,
} from 'entities/shared/common.entities.ts'
import { prepareRguActualLimitsFromRestrictions } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/RightContainer/ui/Station/ui/ExtraSettings/ui/RguLimit/lib/prepareRguActualLimits.ts'
import { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'
import { ItemsProps } from 'shared/ui/Select/Select.tsx'
import { useStore } from 'stores/useStore.ts'
import { IBaseRowData } from 'widgets/TableV1'
import { FilterControlMultiSelectProps, TFilterControlMultiSelectFiltering } from 'widgets/TableV1/ui'

import { RguLimitModalProps } from '../ui'

export interface RguLimitRow extends IBaseRowData {
  active: boolean
  rguItem: ItemsProps
  restrictionItem: ItemsProps
  limit: string
}

export enum RguLimitLoadStatus {
  loading,
  loadedWithData,
  loadedWithoutData,
}

/**
 * Обработка состояния модального окна для создания причин ограничений по РГЕ
 * @param type - Тип ограничения (МАКСИМУМ или МИНИМУМ)
 * @param item - Данные ограничений РГЕ, который получены с бека / подготовлены для отправки на бек
 * @returns Поля и обработчики для формы RguLimitModal
 */
export const useRguLimitModalTable = (
  type: RguLimitModalProps['type'],
  item: IParametersConfigsOutput<IToggleableRguRestrictionLimitParameter['value']['value']>,
) => {
  const { calcModelStore } = useStore()
  const { restriction, initLimits, configs } = calcModelStore

  const initialRows = useRef<RguLimitRow[]>([])
  // Строк, которые отображаются в таблице и видимы для пользователя
  const [rows, setRows] = useState<RguLimitRow[]>([])
  // Все строки, включая изменения после добавления/удаления
  const rowsBeforeFilter = useRef<RguLimitRow[]>([])

  const [selectedIds, setSelectedIds] = useState<(string | number)[]>([])
  const initialSelectedIds = useRef<(string | number)[]>([])

  const [selectedRguId, setSelectedRguId] = useState<string | null>(null)
  const [selectedRestrictionCode, setSelectedRestrictionCode] = useState<string | null>(null)
  const [limit, setLimit] = useState<string>('0')

  const [rgusItemsForFilter, setRgusItemsForFilter] = useState<TFilterControlMultiSelectFiltering['items']>([])

  const [initialDataLoaded, setInitialDataLoaded] = useState<RguLimitLoadStatus>(RguLimitLoadStatus.loading)

  const hasChanges = useMemo(() => {
    const hasEditedRows = JSON.stringify(initialRows.current) !== JSON.stringify(rowsBeforeFilter.current)
    const hasEditedSelectedIds = JSON.stringify(initialSelectedIds.current) !== JSON.stringify(selectedIds)

    return hasEditedRows || hasEditedSelectedIds
  }, [rows, selectedIds])

  // Список с ограничениями причин для использования в компоненте Selector
  const restrictionItems = useMemo(
    () =>
      restriction
        .filter(
          (restrictionItem) =>
            !rowsBeforeFilter.current.some((row) => row.restrictionItem.value === restrictionItem.code),
        )
        .map((restrictionItem) => ({
          label: restrictionItem.name,
          value: restrictionItem.code,
        })),
    [restriction, rows],
  )

  // Список с РГЕ для использования в компоненте Selector
  const rguItems = useMemo(
    () =>
      configs.map((config) => ({
        value: config.id,
        label: config.name,
      })),
    [configs],
  )

  // Инициализация списков: Причины, Параметры фильтрации для РГЕ
  useLayoutEffect(() => {
    initLimits(type === 'RGU_MINIMUM_LIMIT' ? 'min' : 'max', false)
    setRgusItemsForFilter(
      configs.map((config) => ({
        label: config.name,
        value: String(config.id),
        checked: true,
      })),
    )
  }, [])

  // Инициализация параметров (Причина и РГЕ) по умолчанию
  useEffect(() => {
    if (restrictionItems.length) {
      setSelectedRestrictionCode(String(restrictionItems[0].value))
    }
    if (rguItems.length) {
      setSelectedRguId(String(rguItems[0].value))
    }

    // Устанавливаем флаг после загрузки исходных данных.
    // Ставим искусственную задержку, чтобы лоудер не промаргивал в случае быстрого выполнения запросов
    if (restrictionItems.length > 0 && rguItems.length > 0) {
      setTimeout(() => {
        setInitialDataLoaded(RguLimitLoadStatus.loadedWithData)
      }, 600)
    }
    if (rguItems.length === 0) {
      setTimeout(() => {
        setInitialDataLoaded(RguLimitLoadStatus.loadedWithoutData)
      }, 600)
    }
  }, [restrictionItems, rguItems])

  // Инициализация данных по строкам для таблицы после инициализации параметров (Причина и РГЕ) по умолчанию
  useEffect(() => {
    if (!initialDataLoaded) return

    const initRows: RguLimitRow[] = []
    const initSelectedIds: (string | number)[] = []
    if (item.parameterValue.value.value?.restrictions.length) {
      item.parameterValue.value.value.restrictions.forEach((restriction, idx) => {
        const restrictionItem = restrictionItems.find((item) => item.value === Number(restriction.code))
        const rguItem = rguItems.find((item) => item.value === Number(restriction.rguId))

        if (restrictionItem && rguItem) {
          if (idx === 0) {
            setSelectedRguId(String(rguItem.value))
            setSelectedRestrictionCode(String(restrictionItem.value))
          }

          const tabId = restrictionItem.value
          initRows.push({
            tabId,
            restrictionItem,
            rguItem,
            active: restriction.active,
            limit: String(restriction.limit),
            disabledChecked: false,
            isEdit: false,
            children: [],
          })
          if (restriction.active) {
            initSelectedIds.push(tabId)
          }
        }
      })
    }
    setSelectedIds(initSelectedIds)
    setRows(initRows)
    initialSelectedIds.current = initSelectedIds
    initialRows.current = initRows
    rowsBeforeFilter.current = initRows
  }, [item, initialDataLoaded])

  // Обновление данных по строкам таблице, в зависимости от выбранных фильтров
  useEffect(() => {
    const selectedIds = rgusItemsForFilter
      .filter((itemsForFilter) => itemsForFilter.checked)
      .map((itemsForFilter) => itemsForFilter.value)
    setRows(rowsBeforeFilter.current.filter((row) => selectedIds.includes(String(row.rguItem.value))))
  }, [rgusItemsForFilter])

  // Строки обогащенные полем с цветом подкраски. Для случая, если строка является «критичным» ограничением
  const rowsWithHighlighting: RguLimitRow[] = useMemo(() => {
    // Для понимания: критичными ограничениями также являются те ограничения, которые отображаются в таблице на странице РМ,
    // поэтому допустимо переиспользовать метод prepareRguActualLimitsFromRestrictions
    const restrictionFromRows = rowsBeforeFilter.current
      // Изменяем статус активности ячейки для корректного вычисления списка actualLimits
      .map((row) => ({ ...row, active: selectedIds.includes(row.tabId) }))
      // Обогащаем данные обязательными полями
      .map((row) => ({
        ...row,
        rguId: Number(row.rguItem.value!),
        rguName: row.rguItem.label!,
        code: Number(row.restrictionItem.value!),
        name: row.restrictionItem.label!,
        limit: Number(row.limit),
      }))
    const actualLimitIds = prepareRguActualLimitsFromRestrictions(type, restrictionFromRows).map(
      (actualLimit) => actualLimit.code,
    )

    return rows.map((prevRow) => {
      if (actualLimitIds.includes(Number(prevRow.restrictionItem.value!))) {
        return {
          ...prevRow,
          rowColor: 'grayAndBold',
        }
      }

      return {
        ...prevRow,
        rowColor: undefined,
      }
    })
  }, [type, rows, selectedIds])

  // Сброс данных по таблице
  const handleReset = useCallback(() => {
    setRows(initialRows.current)
    rowsBeforeFilter.current = initialRows.current
    setSelectedIds(initialSelectedIds.current)
  }, [])

  // Добавление новой строки в таблице
  const handleAddRestriction = useCallback(() => {
    const restrictionItem = restrictionItems.find((item) => item.value === Number(selectedRestrictionCode))
    const rguItem = rguItems.find((item) => item.value === Number(selectedRguId))

    if (!restrictionItem || !rguItem) return

    const tabId = restrictionItem.value
    const newRow: RguLimitRow = {
      tabId,
      active: true,
      restrictionItem,
      rguItem,
      limit,
      // Нужно для отображения чекбоксов
      disabledChecked: false,
      isEdit: true,
    }
    setSelectedIds((prevIds) => [...prevIds, tabId])
    setRows((prev) => [...prev, newRow])
    rowsBeforeFilter.current = [...rowsBeforeFilter.current, newRow]
  }, [restriction, selectedRguId, selectedRestrictionCode, limit])

  const handleChangeLimit = useCallback((limit: number, row: RguLimitRow) => {
    const _updateRows = (prevRows: RguLimitRow[]): RguLimitRow[] =>
      prevRows.map((prevRow) => {
        if (prevRow.tabId === row.tabId) {
          return {
            ...prevRow,
            limit: String(isNaN(limit) ? 0 : limit),
          }
        }

        return prevRow
      })

    setRows(_updateRows)
    rowsBeforeFilter.current = _updateRows(rowsBeforeFilter.current)
  }, [])

  // Удаление строки в таблице
  const handleDeleteRestriction = useCallback((tabId: number) => {
    setRows((prev) => prev.filter((row) => row.tabId !== tabId))
    rowsBeforeFilter.current = rowsBeforeFilter.current.filter((row) => row.tabId !== tabId)
    setSelectedIds((prev) => prev.filter((id) => id !== tabId))
  }, [])

  // Сохранение данных в модальном окне, которые через callback onConfirm передаются в store calcModelStore
  const handleSave = useCallback(
    (onConfirm?: (res: IToggleableRguRestrictionLimitParameterValue[]) => void) => {
      const result: IToggleableRguRestrictionLimitParameterValue[] = rowsBeforeFilter.current.map((row) => ({
        active: selectedIds.includes(row.tabId),
        rguId: Number(row.rguItem.value),
        rguName: row.rguItem.label!,
        code: Number(row.restrictionItem.value),
        name: row.restrictionItem.label!,
        limit: Number(row.limit),
      }))
      onConfirm?.(result)
    },
    [rows, selectedIds, restrictionItems, rguItems],
  )

  // Обработчик данных по фильтрам
  const handleFilter: Exclude<FilterControlMultiSelectProps['onFilter'], undefined> = useCallback(
    (_, columnName, values) => {
      if (columnName === 'rguItem') {
        setRgusItemsForFilter((prevItems) =>
          prevItems.map((filterItem) => ({
            ...filterItem,
            checked: values.includes(filterItem.value),
          })),
        )
      }
    },
    [],
  )

  // Блокировка кнопки по добавлению новой строк ограничения
  const canAddRestriction = rguItems.length > 0 && restrictionItems.length > 0 && limit.length > 0

  return {
    rows: rowsWithHighlighting,
    selectedIds,
    setSelectedIds,
    selectedRguId,
    setSelectedRguId,
    selectedRestrictionCode,
    setSelectedRestrictionCode,
    limit,
    setLimit,
    hasChanges,
    rgusItemsForFilter,
    initialDataLoaded,
    restrictionItems,
    rguItems,
    canAddRestriction,
    handleReset,
    handleAddRestriction,
    handleChangeLimit,
    handleDeleteRestriction,
    handleSave,
    handleFilter,
  }
}
