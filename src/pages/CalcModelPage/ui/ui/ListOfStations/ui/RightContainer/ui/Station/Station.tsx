import {
  IParametersConfigsOutput,
  ISaveParamsInput,
  ValueConfigsOutputWithGroups,
} from 'entities/api/calcModelPage.entities.ts'
import { TIME_LOADER } from 'entities/constants.ts'
import { PlantParameter } from 'entities/shared/common.entities.ts'
import { observer } from 'mobx-react'
import { HistoryParamsModal } from 'pages/CalcModelPage/ui/ui/HistoryParamsModal'
import { HistoryParamsModalProps } from 'pages/CalcModelPage/ui/ui/HistoryParamsModal/ui/HistoryParamsModal.tsx'
import { validate } from 'pages/CalcModelPage/ui/ui/ListOfStations/lib/validate.ts'
import cls from 'pages/CalcModelPage/ui/ui/ListOfStations/ListOfStations.module.scss'
import { IPropsRight } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/RightContainer'
import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { Button } from 'shared/ui/Button'
import { Loader } from 'shared/ui/Loader'
import { Toggle } from 'shared/ui/Toggle'
import { IPrepareRgu } from 'stores/CalcModelStore'
import { useStore } from 'stores/useStore.ts'

import {
  DisplaySettings,
  ExtraSettings,
  FactualSettings,
  MainSettings,
  TAdjustableUnitValue,
  WarningsModal,
} from './ui'

const characteristicsList = [
  { value: 'main', label: 'Основные х-ки' },
  { value: 'extra', label: 'Дополнительные х-ки' },
  { value: 'displaySettings', label: 'Настройки отображения' },
  { value: 'factualSettings', label: 'Настройки расчёта' },
].filter((tabItem) => !(isHideCMDisplaySettingsTab && tabItem.value === 'displaySettings'))

type TabKey = (typeof characteristicsList)[number]['value']

export const Station = observer((props: IPropsRight) => {
  const {
    editMode,
    setEditMode,
    rows,
    setRows,
    isLoading,
    setIsLoading,
    errors,
    setErrors,
    setIsLoadingTable,
    isEditRows,
    init,
    editModeRole,
  } = props
  const { calcModelStore } = useStore()
  const { getParams, params, configs, saveParams, date, actualStage, listOfStationsStore } = calcModelStore
  const { selectedPlant, selectedPlantId, displaySettingsStore, factualSettingsStore } = listOfStationsStore
  const [isHistoryModal, setIsHistoryModal] = useState<HistoryParamsModalProps['object'] | null>(null)
  const [characteristics, setCharacteristics] = useState<TabKey>('main')
  const [adjustableUnit, setAdjustableUnit] = useState<TAdjustableUnitValue>('PLANT')
  const [optimization, setOptimization] = useState<string | boolean>('true')

  const isEditPlant = selectedPlant?.icon === 'settings' && actualStage?.code

  const prepareData = (arr: IPrepareRgu[], parentId: number): IPrepareRgu[] => {
    return arr.map((el) => {
      const children = el?.children && el?.children?.length > 0 ? prepareData(el?.children, el.id) : []

      return { ...el, tabId: `${parentId}-${el.id}`, children }
    })
  }

  const initialRows = useMemo(() => {
    const rows = prepareData(configs, 0)
    setRows(rows)
    setTimeout(() => {
      setIsLoading(false)
    }, TIME_LOADER)
    setTimeout(() => {
      setIsLoadingTable(false)
    }, TIME_LOADER * 2)

    return rows
  }, [configs])

  useEffect(() => {
    if (selectedPlant && date) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1 > 9 ? date.getMonth() + 1 : `0${date.getMonth() + 1}`
      const day = date.getDate() > 9 ? date.getDate() : `0${date.getDate()}`
      const prepareDate = `${year}-${month}-${day}`
      getParams(selectedPlant.plantId, prepareDate)
    }
  }, [selectedPlant])

  const [paramsFinal, setParamsFinal] = useState<IParametersConfigsOutput<ValueConfigsOutputWithGroups>[]>([])
  const [efficiency, setEfficiency] = useState('1')

  useEffect(() => {
    // При изменении состояния значения params из стора обновляем значения в локальном стейте
    resetMainSettings()
    resetExtraSettings()
  }, [params])

  /**
   * Обрабатывает смену вкладки характеристик.
   * Если текущая вкладка — "Настройки отображения" и есть несохранённые изменения,
   * показывает предупреждение пользователю. Если пользователь подтверждает переход,
   * или изменений нет, переключает вкладку и запускает индикатор загрузки.
   *
   * @param newTab Ключ новой вкладки характеристик
   */
  const handleCharacteristicsChange = (newTab: TabKey) => {
    if (
      displaySettingsStore.isDisplaySettingsChanged ||
      factualSettingsStore.isDisplaySettingsChanged ||
      isEditRows ||
      editMode
    ) {
      const userConfirmed = window.confirm('У вас есть несохраненные изменения. Вы действительно хотите перейти?')

      if (!userConfirmed) {
        return
      }
    }

    setIsLoadingTable(true)
    setCharacteristics(newTab)
    setTimeout(() => {
      setIsLoadingTable(false)
    }, TIME_LOADER)
  }

  const validateLimits = (MIN: number | undefined, MAX: number | undefined) => {
    if (MIN === undefined && MAX === undefined) {
      const textError = `Введите ограничения`
      setErrors((prev) => {
        prev.set('MINIMUM_LIMIT', textError)
        prev.set('MAXIMUM_LIMIT', textError)

        return prev
      })

      return false
    }
    if (MIN === undefined) {
      const textError = `Введите ограничение`
      setErrors((prev) => {
        prev.set('MINIMUM_LIMIT', textError)

        return prev
      })

      return false
    }
    if (MAX === undefined) {
      const textError = `Введите ограничение`
      setErrors((prev) => {
        prev.set('MAXIMUM_LIMIT', textError)

        return prev
      })

      return false
    }
    if (MIN > MAX) {
      const textError = 'Ограничение минимума не может быть больше ограничения максимума'
      setErrors((prev) => {
        prev.set('MINIMUM_LIMIT', textError)
        prev.set('MAXIMUM_LIMIT', textError)

        return prev
      })

      return false
    }

    return true
  }

  const isEFFICIENCY = params.some((el) => el.parameterName === 'EFFICIENCY')

  const reinitDataAfterSave = (plantId: number, prepareDate: string) => {
    init()
    getParams(plantId, prepareDate)
    setEditMode(false)
    setErrors((prev) => {
      prev.clear()

      return prev
    })
  }

  const resetMainSettings = useCallback(() => {
    if (params.length > 0) {
      const curAdjustableUnit = params.find((el) => el.parameterName === 'REGULATED_UNIT')
      if (curAdjustableUnit) {
        setAdjustableUnit(curAdjustableUnit?.parameterValue?.value as unknown as TAdjustableUnitValue)
      }
      const curOptimization = params.find((el) => el.parameterName === 'OPTIMIZATION')
      if (curOptimization) {
        setOptimization(curOptimization.parameterValue.value.turnedOn)
      }
      const curEfficiency = params.find((el) => el.parameterName === 'EFFICIENCY')
      if (curEfficiency) {
        setEfficiency(curEfficiency.parameterValue.value as unknown as string)
      }
    }
    setEditMode(false)
    setRows(initialRows)
  }, [params, initialRows])

  const resetExtraSettings = useCallback(() => {
    if (params.length > 0) {
      const res = params
        .filter(
          (el) =>
            el.parameterName !== 'REGULATED_UNIT' &&
            el.parameterName !== 'OPTIMIZATION' &&
            el.parameterName !== 'EFFICIENCY',
        )
        .sort((a, b) => a.priority - b.priority)
      setParamsFinal(res as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>[])
    }
    setEditMode(false)
    setRows(initialRows)
    setErrors((prev) => {
      prev.clear()

      return prev
    })
  }, [params, initialRows])

  const onSave = async () => {
    // Если мы на вкладке настроек отображения, сохраняем только их
    if (characteristics === 'displaySettings' && displaySettingsStore.isDisplaySettingsChanged) {
      try {
        await displaySettingsStore.saveDisplaySettings()

        return
      } catch (e) {
        console.error('Ошибка при сохранении:', e)

        return
      }
    }

    // Если мы на вкладке настроек расчёта, сохраняем только их
    if (characteristics === 'factualSettings' && factualSettingsStore.isDisplaySettingsChanged) {
      try {
        await factualSettingsStore.saveFactualSettings()

        return
      } catch (e) {
        console.error('Ошибка при сохранении:', e)

        return
      }
    }
    const optim = params.find((el) => el.parameterName === PlantParameter.OPTIMIZATION)
    const adj = params.find((el) => el.parameterName === PlantParameter.REGULATED_UNIT)
    const eff = params.find((el) => el.parameterName === PlantParameter.EFFICIENCY)

    const isOptim =
      optim?.parameterValue?.value?.turnedOn !== undefined &&
      String(optim?.parameterValue?.value?.turnedOn) !== String(optimization)
    const isAdj =
      adj?.parameterValue?.value !== undefined && (adj?.parameterValue?.value as unknown as string) !== adjustableUnit
    const isEff = isEFFICIENCY && eff && (eff.parameterValue.value as unknown as string) !== efficiency

    const parameters = [
      ...paramsFinal
        .filter((el) => el?.isEdit)
        .map((el) => {
          if (el.parameterName === 'PRESSURE_RECESSION_WATCH') {
            return {
              parameterName: el.parameterName,
              parameterValue: {
                ...el.parameterValue,
                value: {
                  ...el.parameterValue.value,
                  value: {
                    ...el.parameterValue.value.value,
                    residualOutput:
                      el.parameterValue.value.value?.residualOutput === undefined ||
                      el.parameterValue.value.value?.residualOutput === '' ||
                      el.parameterValue.value.value?.residualOutput === '0'
                        ? null
                        : Number(el.parameterValue.value.value.residualOutput),
                    lossPortionRatio:
                      el.parameterValue.value.value?.lossPortionRatio === undefined ||
                      el.parameterValue.value.value.lossPortionRatio === '' ||
                      el.parameterValue.value.value.lossPortionRatio === '0'
                        ? null
                        : Number(el.parameterValue.value.value.lossPortionRatio),
                    generatorPower:
                      el.parameterValue.value.value?.generatorPower === undefined ||
                      el.parameterValue.value.value.generatorPower === '' ||
                      el.parameterValue.value.value.generatorPower === '0'
                        ? null
                        : Number(el.parameterValue.value.value.generatorPower),
                  },
                },
              },
            }
          }
          if (el.parameterName === 'LOAD_UNLOAD_SPEED') {
            const loadSpeed = el.parameterValue.value.value?.loadSpeed
              ? String(el.parameterValue.value.value?.loadSpeed)
              : ''
            const unloadSpeed = el.parameterValue.value.value?.unloadSpeed
              ? String(el.parameterValue.value.value?.unloadSpeed)
              : ''

            return {
              parameterName: el.parameterName,
              parameterValue: {
                ...el.parameterValue,
                value: {
                  ...el.parameterValue.value,
                  value: {
                    ...el.parameterValue.value.value,
                    loadSpeed,
                    unloadSpeed,
                  },
                },
              },
            }
          }
          if (el.parameterName === 'GENERATOR_JOINT_WORK_WATCH') {
            return {
              parameterName: el.parameterName,
              parameterValue: {
                ...el.parameterValue,
                value: {
                  ...el.parameterValue.value,
                  value: el.parameterValue.value.value.groups.length === 0 ? null : el.parameterValue.value.value,
                },
              },
            }
          }
          if (['PARTICIPATION_AVRCHM', 'PARTICIPATION_NPRCH'].includes(el.parameterName)) {
            return {
              parameterName: el.parameterName,
              parameterValue: {
                ...el.parameterValue,
                value: {
                  ...el.parameterValue.value,
                  value:
                    (el.parameterValue.value.value as unknown as string) === '' ||
                    Number(el.parameterValue.value.value as unknown as string) === 0
                      ? null
                      : el.parameterValue.value.value,
                },
              },
            }
          }
          if (el.parameterName === 'E_MAX_E_MIN') {
            return {
              parameterName: el.parameterName,
              parameterValue: {
                ...el.parameterValue,
                value: {
                  ...el.parameterValue.value,
                  value:
                    (el.parameterValue.value.value as unknown as string) === '' ||
                    (el.parameterValue.value.value as unknown as string) === '0'
                      ? null
                      : parseFloat(el.parameterValue.value.value as unknown as string) * 1000,
                },
              },
            }
          }

          return {
            parameterName: el.parameterName,
            parameterValue: el.parameterValue,
          }
        }),
    ]
    if (isOptim) {
      const optimObject = {
        ...optim,
        parameterValue: {
          ...optim.parameterValue,
          value: {
            turnedOn: JSON.parse(optimization as string),
          },
        },
      } as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>
      parameters.push(optimObject as any)
    }
    if (isAdj) {
      const adjObject = {
        ...adj,
        parameterValue: {
          ...adj.parameterValue,
          value: adjustableUnit,
        },
      } as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>
      parameters.push(adjObject as any)
    }
    if (isEff) {
      const effObject = {
        ...eff,
        parameterValue: {
          ...eff?.parameterValue,
          value: Number(efficiency),
        },
      } as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>
      parameters.push(effObject as any)
    }

    const generatorsRows = (arr: IPrepareRgu[]) => {
      const newRows = []

      for (const iterator of arr) {
        if (iterator.children) {
          newRows.push(...iterator.children)
        }
      }

      return newRows
    }

    const res = {
      id: selectedPlantId,
      parameters,
      rgus: [
        ...rows
          .filter((el) => el?.isEdit)
          .map((el) => {
            const parameters = el?.parameters?.map((par) => {
              if (
                par.parameterName === 'VOLTAGE' &&
                (par.parameterValue.value as unknown as number) !== Number(el.voltage)
              ) {
                if (el.voltage || el.voltage === '') {
                  return {
                    ...par,
                    parameterValue: {
                      ...par.parameterValue,
                      value: Number(el.voltage) || null,
                      edited: true,
                    },
                  }
                } else {
                  const res = par.parameterValue ? par.parameterValue : { parameterValue: null }

                  return res
                }
              }
              if (par.parameterName === 'PARTICIPATION_AVRCHM' && par.parameterValue.value.turnedOn !== el.avrchm) {
                return {
                  ...par,
                  parameterValue: {
                    ...par.parameterValue,
                    value: {
                      ...par.parameterValue.value,
                      turnedOn: el.avrchm,
                    },
                    edited: true,
                  },
                }
              }
              if (par.parameterName === 'PRIORITY_LOAD' && par.parameterValue.value.turnedOn !== el.priorityLoad) {
                return {
                  ...par,
                  parameterValue: {
                    ...par.parameterValue,
                    value: {
                      turnedOn: el.priorityLoad,
                    },
                    edited: true,
                  },
                }
              }

              return par
            })
            const temp = parameters as unknown as {
              parameterValue: {
                edited: boolean
                type: string
                value: string
              }
            }[]
            const filteredParameters = temp
              ? temp
                  .filter((item) => item?.parameterValue?.edited)
                  .map((item) => ({
                    ...item,
                    parameterValue: {
                      type: item?.parameterValue?.type,
                      value: item?.parameterValue?.value,
                    },
                  }))
              : []

            const startDate = el.startDate ? el.startDate.split('.').reverse().join('-') : null
            const endDate = el.endDate ? el.endDate.split('.').reverse().join('-') : null

            return {
              id: el.id,
              parameters: filteredParameters,
              startDate,
              endDate,
            }
          }),
      ],
      generators: [
        ...generatorsRows(rows)
          .filter((el) => el.isEdit)
          .map((el) => {
            const parameters = el.parameters
              ? el.parameters.map(
                  (par: {
                    parameterName: string
                    parameterValue: {
                      value: { turnedOn: boolean }
                    }
                  }) => {
                    if (
                      par.parameterName === 'PARTICIPATION_AVRCHM' &&
                      par.parameterValue.value.turnedOn !== el.avrchm
                    ) {
                      return {
                        ...par,
                        parameterValue: {
                          ...par.parameterValue,
                          value: {
                            ...par.parameterValue.value,
                            turnedOn: el.avrchm,
                          },
                        },
                      }
                    }
                    if (
                      par.parameterName === 'PRIORITY_LOAD' &&
                      par.parameterValue.value.turnedOn !== el.priorityLoad
                    ) {
                      return {
                        ...par,
                        parameterValue: {
                          ...par.parameterValue,
                          value: {
                            turnedOn: el.priorityLoad,
                          },
                        },
                      }
                    }

                    return par
                  },
                )
              : []
            const startDate = el.startDate ? el.startDate.split('.').reverse().join('-') : null
            const endDate = el.endDate ? el.endDate.split('.').reverse().join('-') : null

            return {
              id: el.id,
              parameters,
              startDate,
              endDate,
            }
          }),
      ],
    }

    const year = date.getFullYear()
    const month = date.getMonth() + 1 > 9 ? date.getMonth() + 1 : `0${date.getMonth() + 1}`
    const day = date.getDate() > 9 ? date.getDate() : `0${date.getDate()}`
    const prepareDate = `${year}-${month}-${day}`

    const err = validate(parameters, paramsFinal, optimization as unknown as string)

    setErrors(err)
    if (err.size) {
      return
    }
    const INIT_MIN = paramsFinal?.find((el) => el?.parameterName === 'MINIMUM_LIMIT')?.parameterValue?.value?.value
      ?.actualLimit
    const INIT_MAX = paramsFinal?.find((el) => el?.parameterName === 'MAXIMUM_LIMIT')?.parameterValue?.value?.value
      ?.actualLimit

    const checkLimit = (limit: unknown) => {
      const limitType = typeof limit
      if ((limitType === 'number' || limitType === 'string') && !Number.isNaN(Number(limit))) return Number(limit)

      return undefined
    }

    const LIMIT_MIN = checkLimit(INIT_MIN)
    const LIMIT_MAX = checkLimit(INIT_MAX)
    const TURN_ON_MIN =
      paramsFinal?.find((el) => el?.parameterName === 'MINIMUM_LIMIT')?.parameterValue?.value?.turnedOn ?? false
    const TURN_ON_MAX =
      paramsFinal?.find((el) => el?.parameterName === 'MAXIMUM_LIMIT')?.parameterValue?.value?.turnedOn ?? false
    const isValidate = TURN_ON_MIN && TURN_ON_MAX ? validateLimits(LIMIT_MIN, LIMIT_MAX) : true

    if (isValidate) {
      await saveParams(res as unknown as ISaveParamsInput, prepareDate).then(() => {
        if (selectedPlant?.plantId) {
          reinitDataAfterSave(selectedPlant?.plantId, prepareDate)
        }
      })
    }
  }

  const onReset = () => {
    // Если мы на вкладке настроек отображения, сбрасываем только их
    if (characteristics === 'displaySettings' && displaySettingsStore.isDisplaySettingsChanged) {
      displaySettingsStore.resetDisplaySettings()
    }

    // Если мы на вкладке настроек расчёта, сбрасываем только их
    if (characteristics === 'factualSettings' && factualSettingsStore.isDisplaySettingsChanged) {
      factualSettingsStore.resetChanges()
    }

    // Если мы на вкладке настроек основных характеристик, сбрасываем только их
    if (characteristics === 'main' && (isEditRows || editMode)) {
      resetMainSettings()
    }

    // Если мы на вкладке настроек дополнительных характеристик, сбрасываем только их
    if (characteristics === 'extra' && (isEditRows || editMode)) {
      resetExtraSettings()
    }
  }

  useHotkeys(
    'ctrl+shift+s',
    () =>
      (editMode ||
        isEditRows ||
        (characteristics === 'displaySettings' && displaySettingsStore.isDisplaySettingsChanged) ||
        (characteristics === 'factualSettings' && factualSettingsStore.isDisplaySettingsChanged)) &&
      onSave(),
    { enableOnFormTags: true },
  )
  useHotkeys(
    'ctrl+shift+x',
    () =>
      (editMode ||
        isEditRows ||
        (characteristics === 'displaySettings' && displaySettingsStore.isDisplaySettingsChanged) ||
        (characteristics === 'factualSettings' && factualSettingsStore.isDisplaySettingsChanged)) &&
      onReset(),
    { enableOnFormTags: true },
  )

  const tabs: Record<TabKey, ReactNode> = {
    main: (
      <MainSettings
        isEditPlant={isEditPlant}
        editModeRole={editModeRole}
        rows={rows}
        setRows={setRows}
        setIsHistoryModal={setIsHistoryModal}
        prepareData={prepareData}
        initialRows={initialRows}
        adjustableUnit={adjustableUnit}
        setAdjustableUnit={setAdjustableUnit}
        setEditMode={setEditMode}
        optimization={optimization}
        setOptimization={setOptimization}
        isEFFICIENCY={isEFFICIENCY}
        efficiency={efficiency}
        setEfficiency={setEfficiency}
        isLoadingTable={isLoading}
        onReset={resetMainSettings}
      />
    ),
    extra: (
      <ExtraSettings
        setParamsFinal={setParamsFinal}
        paramsFinal={paramsFinal}
        errors={errors}
        setIsHistoryModal={setIsHistoryModal}
        optimization={optimization}
        adjustableUnit={adjustableUnit}
        isEditPlant={isEditPlant}
        editModeRole={editModeRole}
        setEditMode={setEditMode}
        editMode={editMode}
        setErrors={setErrors}
        onReset={resetExtraSettings}
      />
    ),
    displaySettings: <DisplaySettings />,
    factualSettings: <FactualSettings />,
  }

  return (
    <div className={cls.ListOfStations}>
      <div className={cls.Right}>
        {isLoading ? (
          <div className={cls.LoaderContainer}>
            <Loader />
          </div>
        ) : (
          <>
            {selectedPlant ? (
              <div className={cls.DataContainer}>
                <div className={cls.HeaderRight}>
                  <div className={cls.HeaderTitle}>{selectedPlant.label}</div>
                  <div>
                    <Toggle
                      items={characteristicsList}
                      value={characteristics}
                      setValue={handleCharacteristicsChange}
                    />
                  </div>
                  <div className={cls.Actions}>
                    <Button
                      disabled={
                        !(
                          editMode ||
                          isEditRows ||
                          (characteristics === 'displaySettings' && displaySettingsStore.isDisplaySettingsChanged) ||
                          (characteristics === 'factualSettings' && factualSettingsStore.isFactualSettingsLoading)
                        )
                      }
                      variant='outlined'
                      onClick={onReset}
                    >
                      Сбросить
                    </Button>
                    <Button
                      disabled={
                        !(
                          editMode ||
                          isEditRows ||
                          (characteristics === 'displaySettings' && displaySettingsStore.isDisplaySettingsChanged) ||
                          (characteristics === 'factualSettings' && factualSettingsStore.isFactualSettingsLoading)
                        )
                      }
                      onClick={onSave}
                    >
                      Сохранить
                    </Button>
                  </div>
                </div>
                {tabs[characteristics]}
              </div>
            ) : (
              <div className={cls.NoData}>Выберите станцию</div>
            )}
          </>
        )}
      </div>
      {isHistoryModal && (
        <HistoryParamsModal
          subtitle={selectedPlant?.label ?? ''}
          object={isHistoryModal}
          onClose={() => setIsHistoryModal(null)}
        />
      )}
      <WarningsModal reinitDataAfterSave={reinitDataAfterSave} />
    </div>
  )
})
