import { IParametersConfigsOutput } from 'entities/api/calcModelPage.entities.ts'
import {
  IToggleableRguRestrictionLimitParameter,
  IToggleableRguRestrictionLimitParameterValue,
  PlantParameter,
} from 'entities/shared/common.entities.ts'
import { observer } from 'mobx-react'
import { type FC, useCallback, useState } from 'react'
import { Button } from 'shared/ui/Button'

import { RguLimitModal, RguLimitTable } from './ui'

interface RguLimitProps {
  type: PlantParameter.RGU_MINIMUM_LIMIT | PlantParameter.RGU_MAXIMUM_LIMIT
  item: IParametersConfigsOutput<IToggleableRguRestrictionLimitParameter['value']['value']>
  disabled: boolean
  hasChanges: boolean
  onChange?: (parameterName: string, value: IToggleableRguRestrictionLimitParameterValue[], mode: string) => void
}

export const RguLimit: FC<RguLimitProps> = observer((props) => {
  const { item, type, disabled, hasChanges, onChange } = props
  const [showModal, setShowModal] = useState<boolean>(false)

  const openModal = useCallback(() => setShowModal(true), [])

  const closeModal = useCallback(() => setShowModal(false), [])

  const handleConfirm = useCallback(
    (result: IToggleableRguRestrictionLimitParameterValue[]) => {
      onChange?.(type, result, '')
      setShowModal(false)
    },
    [type],
  )

  return (
    <>
      {item.parameterValue.value.turnedOn && (
        <div>
          <Button variant='text' onClick={openModal}>
            Список ограничений
          </Button>
          <RguLimitTable type={type} item={item} hasChanges={hasChanges} />
        </div>
      )}
      {showModal && (
        <RguLimitModal type={type} disabled={disabled} item={item} onClose={closeModal} onConfirm={handleConfirm} />
      )}
    </>
  )
})
