import { IconButton } from '@mui/material'
import { Icon } from 'shared/ui'
import { type IColumn } from 'widgets/TableV1'
import { TFilterControlMultiSelectFiltering } from 'widgets/TableV1/ui'

import cls from '../ui/RguLimitModal/RguLimitModal.module.scss'
import { numberOption } from './numberOption'
import { RguLimitRow } from './useRguLimitModalTable.ts'

type RguLimitModalColumns = (
  hasEditAccess: boolean,
  disabled: boolean,
  rgusForFilter: TFilterControlMultiSelectFiltering['items'],
  onDelete: (tabId: RguLimitRow['tabId']) => void,
  onChangeLimit: (limit: number, row: RguLimitRow) => void,
) => IColumn<RguLimitRow>[]

/**
 *
 * @param hasEditAccess - Флаг, доступности редактирования для пользователя с необходимой ролью
 * @param disabled - Флаг, доступности редактирования, если не выполнены любые другие условия, кроме наличия роли
 * @param rgusForFilter - Параметры РГЕ, которые используются для фильтрации
 * @param onDelete - callback для удаления записи
 * @param onChangeLimit - callback для обработки изменения Значения напрямую в таблице
 * @returns Массив сконфигурированных колонок для компонента RguLimitModal
 */
export const rguLimitModalColumns: RguLimitModalColumns = (
  hasEditAccess,
  disabled,
  rgusForFilter,
  onDelete,
  onChangeLimit,
): IColumn<RguLimitRow>[] => {
  const columns: IColumn<RguLimitRow>[] = [
    {
      name: 'rguItem',
      title: 'РГЕ',
      width: 290,
      filtering: {
        type: 'multiselect',
        items: rgusForFilter,
      },
      render: (_, row) => row.rguItem.label,
    },
    {
      name: 'restrictionItem',
      title: 'Причина',
      width: 322,
      render: (_, row) => row.restrictionItem.label,
    },
    {
      name: 'limit',
      title: 'Значение , МВт',
      width: 190,
      editing: {
        type: 'number',
        enabled: hasEditAccess && !disabled,
        numberOption,
        onAfterChange: (changedValue, row) => onChangeLimit(changedValue, row),
      },
    },
  ]

  if (hasEditAccess) {
    columns.push({
      name: 'actions',
      title: ' ',
      width: 40,
      render: (_, row) => (
        <div className={cls.trashButtonContainer}>
          <IconButton onClick={() => onDelete(row.tabId)} className={cls.trashButton} disabled={disabled}>
            <Icon height={12} name='trash' />
          </IconButton>
        </div>
      ),
    })
  }

  return columns
}
