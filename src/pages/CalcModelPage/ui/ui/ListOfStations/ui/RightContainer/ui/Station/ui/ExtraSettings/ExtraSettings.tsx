import AddIcon from '@mui/icons-material/Add'
import { I<PERSON><PERSON><PERSON><PERSON>, Tooltip } from '@mui/material'
import {
  ExValueConfigsOutput,
  IParametersConfigsOutput,
  IRestriction,
  ValueConfigsOutput,
  ValueConfigsOutputWithGroups,
} from 'entities/api/calcModelPage.entities.ts'
import {
  IToggleableRestrictionCorridorParameter,
  IToggleableRguRestrictionLimitParameter,
  PlantParameter,
} from 'entities/shared/common.entities.ts'
import { observer } from 'mobx-react'
import { AddGroupsModal } from 'pages/CalcModelPage/ui/ui/AddGroupsModal'
import { IObj } from 'pages/CalcModelPage/ui/ui/AddGroupsModal/ui/AddGroupsModal.tsx'
import { DeleteModal } from 'pages/CalcModelPage/ui/ui/DeleteModal'
import { HistoryParamsModalProps } from 'pages/CalcModelPage/ui/ui/HistoryParamsModal/ui/HistoryParamsModal.tsx'
import cls from 'pages/CalcModelPage/ui/ui/ListOfStations/ListOfStations.module.scss'
import { GroupsList } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/GroupsList'
import { IItem } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/GroupsList/GroupsList.tsx'
import { IPropsRight } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/RightContainer'
import { Dispatch, FC, SetStateAction, useEffect, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { prepareFlatData } from 'shared/lib/prepareData'
import { Icon } from 'shared/ui'
import { Button } from 'shared/ui/Button'
import { Select } from 'shared/ui/Select'
import { Switch } from 'shared/ui/Switch'
import { TextField } from 'shared/ui/TextField'
import { useStore } from 'stores/useStore.ts'

import { TAdjustableUnitValue } from '../MainSettings'
import { LimitModal, RestrictionCorridor, RguLimit } from './ui'

type TKeysForItem = 'E_MAX_E_MIN' | 'REGULATED_UNIT' | 'CONSUMPTION_SCHEDULE_BINDING' | 'RGU_GROUP'

type TItem<T> = T extends TKeysForItem
  ? IParametersConfigsOutput<string>
  : T extends 'RESTRICTION_CORRIDOR'
    ? IParametersConfigsOutput<IToggleableRestrictionCorridorParameter['value']['value']>
    : IParametersConfigsOutput<ValueConfigsOutputWithGroups>

type ICreateGroup = object

interface ILimitModal {
  mode: string
  restrictions: IRestriction[]
}

interface ExtraSettingsProps {
  setParamsFinal: Dispatch<SetStateAction<IParametersConfigsOutput<ValueConfigsOutputWithGroups>[]>>
  paramsFinal: IParametersConfigsOutput<ValueConfigsOutputWithGroups>[]
  errors: IPropsRight['errors']
  setErrors: IPropsRight['setErrors']
  setIsHistoryModal: Dispatch<SetStateAction<HistoryParamsModalProps['object'] | null>>
  optimization: string | boolean
  adjustableUnit: TAdjustableUnitValue
  isEditPlant?: string | boolean
  editModeRole: IPropsRight['editModeRole']
  editMode: IPropsRight['editMode']
  setEditMode: IPropsRight['setEditMode']
  onReset: () => void
}

export const ExtraSettings: FC<ExtraSettingsProps> = observer((props) => {
  const {
    setParamsFinal,
    paramsFinal,
    errors,
    setIsHistoryModal,
    optimization,
    adjustableUnit,
    isEditPlant,
    editModeRole,
    editMode,
    setEditMode,
    setErrors,
    onReset,
  } = props
  const { calcModelStore } = useStore()
  const { listOfStationsStore, configs, energyDistrictForParams, isPastDate } = calcModelStore
  const { selectedPlant } = listOfStationsStore
  const [createGroup, setCreateGroup] = useState<null | ICreateGroup>(null)
  const [deleteGroup, setDeleteGroup] = useState<IItem | null>(null)
  const [isLimitModal, setIsLimitModal] = useState<null | ILimitModal>(null)

  useEffect(() => onReset, [onReset])

  const ggAdds = prepareFlatData(configs).map((el) => ({
    ...el,
    type: el?.generators ? 'GEN' : 'STATION',
  }))
  const groupsArr =
    paramsFinal
      .find((el) => el.parameterName === 'GENERATOR_JOINT_WORK_WATCH')
      ?.parameterValue?.value?.value?.groups.map((el) => {
        const children = el?.childs?.length > 0 ? el?.childs : []

        return { ...el, children }
      }) ?? []
  const groups = groupsArr?.length > 0 ? prepareFlatData(groupsArr).filter((el) => el.id) : []
  const ggsAdd = ggAdds
    .filter((el) => el.type === 'STATION')
    .filter((el) => {
      return !groups.some((item) => (item.id as unknown as number) === el.id)
    })

  const regulatedUnits = [
    { value: 'PLANT', label: 'Станция' },
    { value: 'GENERATOR', label: 'Генератор' },
    { value: 'RGU', label: 'РГЕ' },
  ]

  const changeValue = (type: string, value: ValueConfigsOutput, subType: string) => {
    setParamsFinal((prev) => {
      return prev.map((item) => {
        if (item.parameterName === type) {
          const { parameterValue } = item
          if (
            type === 'PARTICIPATION_NPRCH' ||
            type === 'PARTICIPATION_AVRCHM' ||
            type === 'RGU_GROUP' ||
            type === 'EFFICIENCY' ||
            type === 'E_MAX_E_MIN' ||
            type === 'P_GEN_DELTA' ||
            type === 'TERTIARY_RESERVE'
          ) {
            return {
              ...item,
              parameterValue: { ...parameterValue, value: { ...parameterValue.value, value } },
              isEdit: true,
            }
          }
          if (type === 'PRESSURE_RECESSION_WATCH' || type === 'LOAD_UNLOAD_SPEED' || type === 'RESTRICTION_CORRIDOR') {
            return {
              ...item,
              parameterValue: {
                ...parameterValue,
                value: { ...parameterValue.value, value: { ...parameterValue.value.value, [subType]: value } },
              },
              isEdit: true,
            }
          }
          if (item.parameterName === 'MAXIMUM_LIMIT' || item.parameterName === 'MINIMUM_LIMIT') {
            return {
              ...item,
              parameterValue: {
                ...parameterValue,
                value: { ...parameterValue.value, value: { ...parameterValue?.value?.value, actualLimit: value } },
              },
              isEdit: true,
            }
          }
          if (item.parameterName === 'RGU_MAXIMUM_LIMIT' || item.parameterName === 'RGU_MINIMUM_LIMIT') {
            return {
              ...item,
              parameterValue: {
                ...parameterValue,
                value: { ...parameterValue.value, value: { restrictions: value } },
              },
              isEdit: true,
            }
          }
          if (item.parameterName === 'CONSUMPTION_SCHEDULE_BINDING') {
            return {
              ...item,
              parameterValue: { ...parameterValue, value: { ...parameterValue.value, value: { id: value } } },
              isEdit: true,
            }
          }

          return { ...item, parameterValue: { ...parameterValue, value }, isEdit: true }
        }

        return item
      }) as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>[]
    })
  }

  const getExtraContent = (type: string, item: TItem<string>) => {
    switch (type) {
      case PlantParameter.P_GEN_DELTA:
      case PlantParameter.TERTIARY_RESERVE:
      case PlantParameter.E_MAX_E_MIN:
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <TextField
                disabled={!isEditPlant || !editModeRole || isPastDate}
                className={cls.EminMax}
                label='Абсолютное значение, млн. кВт*ч'
                type='number'
                toFixed={3}
                positiveNumber
                value={item?.parameterValue?.value?.value ? String(item.parameterValue.value.value) : ''}
                onChange={(e) => {
                  const value = e.target.value
                  changeValue(type, value, '')
                  setEditMode(true)
                }}
              />
            )}
          </>
        )
      case PlantParameter.REGULATED_UNIT:
        return (
          <>
            <Select
              variant='outlined'
              className={cls.Ter}
              items={regulatedUnits}
              disabled={!isEditPlant || !editModeRole || isPastDate}
              value={item?.parameterValue?.value?.value ? (item?.parameterValue?.value as unknown as string) : 'PLANT'}
              onChange={(value) => {
                changeValue('REGULATED_UNIT', value, '')
                setEditMode(true)
              }}
            />
          </>
        )
      case PlantParameter.OPTIMIZATION:
        return <></>
      case PlantParameter.CONSUMPTION_SCHEDULE_BINDING:
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <Select
                disabled={!isEditPlant || !editModeRole || isPastDate}
                variant='outlined'
                className={cls.Ter}
                items={
                  energyDistrictForParams.some((el) => el.id === item?.parameterValue?.value?.value?.id)
                    ? energyDistrictForParams
                    : item?.parameterValue?.value?.value?.id && item?.parameterValue?.value?.value?.name
                      ? [
                          {
                            ...item.parameterValue.value.value,
                            label: item.parameterValue.value.value.name,
                            value: item.parameterValue.value.value.id,
                          },
                          ...energyDistrictForParams,
                        ]
                      : energyDistrictForParams
                }
                value={item?.parameterValue?.value?.value?.id ? String(item?.parameterValue?.value?.value?.id) : ' '}
                label='Территория'
                onChange={(value) => {
                  changeValue('CONSUMPTION_SCHEDULE_BINDING', value, '')
                  setEditMode(true)
                }}
              />
            )}
          </>
        )
      case PlantParameter.PARTICIPATION_NPRCH:
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <TextField
                disabled={!isEditPlant || !editModeRole || isPastDate}
                className={classNames(cls.LimitCell, { [cls.ExtraRowError]: errors.has('PARTICIPATION_NPRCH') })}
                label='Абсолютное значение, МВт'
                type='number'
                toFixed={3}
                positiveNumber
                value={item?.parameterValue?.value?.value ? String(item.parameterValue.value.value) : ''}
                onChange={(e) => {
                  const value = e.target.value
                  changeValue('PARTICIPATION_NPRCH', value, '')
                  setEditMode(true)
                }}
              />
            )}
          </>
        )
      case PlantParameter.PARTICIPATION_AVRCHM:
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <TextField
                disabled={!isEditPlant || !editModeRole || isPastDate}
                className={cls.LimitCell}
                label='Абсолютное значение, МВт'
                type='number'
                toFixed={3}
                positiveNumber
                maxLength={5}
                value={item.parameterValue.value.value && String(item?.parameterValue?.value?.value)}
                onChange={(e) => {
                  const value = e.target.value
                  if (Number(value) >= 0) {
                    changeValue('PARTICIPATION_AVRCHM', value, '')
                    setEditMode(true)
                  }
                }}
              />
            )}
          </>
        )
      case PlantParameter.GENERATOR_JOINT_WORK_WATCH:
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <>
                {isEditPlant && editModeRole && !isPastDate && (
                  <Button
                    variant='text'
                    className={cls.AddButton}
                    onClick={() => {
                      setCreateGroup({})
                      errors.delete('GENERATOR_JOINT_WORK_WATCH')
                    }}
                  >
                    <>Добавить</>
                    <AddIcon width={16} />
                  </Button>
                )}
                <GroupsList
                  disabled={!isEditPlant || !editModeRole || isPastDate}
                  items={item?.parameterValue?.value?.value?.groups ?? []}
                  setDeleteGroup={setDeleteGroup}
                  setCreateGroup={setCreateGroup}
                  errors={errors}
                />
              </>
            )}
          </>
        )
      case PlantParameter.MINIMUM_LIMIT:
        return (
          <div className={cls.OtherCell}>
            {item.parameterValue.value.turnedOn && (
              <>
                <TextField
                  disabled
                  className={cls.LimitCell}
                  label='МВт'
                  type='number'
                  toFixed={3}
                  positiveNumber
                  value={item?.parameterValue?.value?.value?.actualLimit}
                  onChange={(e) => {
                    changeValue('MINIMUM_LIMIT', e.target.value, '')
                    setEditMode(true)
                  }}
                  inputProps={{ readOnly: true }}
                />
                <Button
                  variant='text'
                  className={cls.ExtraButton}
                  onClick={() => {
                    setIsLimitModal({
                      mode: 'min',
                      ...selectedPlant,
                      restrictions: item?.parameterValue?.value?.value?.restrictions,
                    })
                    setErrors((prev) => {
                      prev.clear()

                      return prev
                    })
                  }}
                >
                  Список ограничений
                </Button>
              </>
            )}
          </div>
        )
      case PlantParameter.MAXIMUM_LIMIT:
        return (
          <div className={cls.OtherCell}>
            {item.parameterValue.value.turnedOn && (
              <>
                <TextField
                  disabled
                  className={cls.LimitCell}
                  label='МВт'
                  type='number'
                  toFixed={3}
                  positiveNumber
                  value={item?.parameterValue?.value?.value?.actualLimit}
                  onChange={(e) => {
                    changeValue('MAXIMUM_LIMIT', e.target.value, '')
                    setEditMode(true)
                  }}
                  inputProps={{ readOnly: true }}
                />
                <Button
                  variant='text'
                  className={cls.ExtraButton}
                  onClick={() => {
                    setIsLimitModal({
                      mode: 'max',
                      ...selectedPlant,
                      restrictions: item?.parameterValue?.value?.value?.restrictions,
                    })
                    setErrors((prev) => {
                      prev.clear()

                      return prev
                    })
                  }}
                >
                  Список ограничений
                </Button>
              </>
            )}
          </div>
        )
      case PlantParameter.RGU_MINIMUM_LIMIT:
      case PlantParameter.RGU_MAXIMUM_LIMIT:
        return (
          <RguLimit
            type={type}
            item={
              item as unknown as IParametersConfigsOutput<IToggleableRguRestrictionLimitParameter['value']['value']>
            }
            disabled={!isEditPlant || !editModeRole || isPastDate}
            hasChanges={editMode}
            onChange={(...args) => {
              changeValue(...args)
              setEditMode(true)
            }}
          />
        )
      case PlantParameter.PRESSURE_RECESSION_WATCH:
        return item.parameterValue.value.turnedOn ? (
          <div className={cls.FLOOD_CONTAINER}>
            <TextField
              disabled={!isEditPlant || !editModeRole || isPastDate}
              label='Величина остаточной выработки, МВт*ч'
              type='number'
              value={
                item?.parameterValue?.value?.value?.residualOutput &&
                String(item?.parameterValue?.value?.value?.residualOutput)
              }
              className={cls.FloodCell}
              onChange={(e) => {
                const value = e.target.value
                if (Number(value) >= 0) {
                  changeValue('PRESSURE_RECESSION_WATCH', value, 'residualOutput')
                  setEditMode(true)
                }
              }}
            />
            <TextField
              label='Коэффициент, определяющий долю потерь на снижение потерь на снижение напора в каждом ГГ, о.е'
              type='number'
              value={item?.parameterValue?.value?.value?.lossPortionRatio ?? ''}
              className={cls.FloodCell}
              disabled={!isEditPlant || !editModeRole || isPastDate}
              onChange={(e) => {
                const value = e.target.value
                if (Number(value) >= 0) {
                  changeValue('PRESSURE_RECESSION_WATCH', value, 'lossPortionRatio')
                  setEditMode(true)
                }
              }}
            />
            <TextField
              label='Мощность ГГ в генераторном режиме, МВт'
              type='number'
              value={item?.parameterValue?.value?.value?.generatorPower ?? ''}
              disabled={!isEditPlant || !editModeRole || isPastDate}
              className={cls.FloodCell}
              onChange={(e) => {
                const value = e.target.value
                if (Number(value) >= 0) {
                  changeValue('PRESSURE_RECESSION_WATCH', value, 'generatorPower')
                  setEditMode(true)
                }
              }}
            />
          </div>
        ) : (
          <></>
        )
      case PlantParameter.LOAD_UNLOAD_SPEED:
        return item.parameterValue.value.turnedOn ? (
          <div className={classNames(cls.FLOOD_CONTAINER, { [cls.LOAD_UNLOAD_SPEED]: true }, [])}>
            <TextField
              disabled={!isEditPlant || !editModeRole || isPastDate}
              label='Скорость набора нагрузки, МВт/час'
              type='number'
              value={item?.parameterValue?.value?.value?.loadSpeed}
              className={cls.EminMax}
              onChange={(e) => {
                const value = e.target.value.replace(/[^0-9.]+/g, '')
                if (value !== '') {
                  changeValue('LOAD_UNLOAD_SPEED', value === '' ? undefined : value, 'loadSpeed')
                  setEditMode(true)
                } else {
                  changeValue('LOAD_UNLOAD_SPEED', value, 'loadSpeed')
                  setEditMode(true)
                }
              }}
            />
            <TextField
              disabled={!isEditPlant || !editModeRole || isPastDate}
              label='Скорость снижения нагрузки, МВт/час'
              type='number'
              value={item?.parameterValue?.value?.value?.unloadSpeed}
              className={cls.EminMax}
              onChange={(e) => {
                const value = e.target.value.replace(/[^0-9.]+/g, '')
                if (value !== '') {
                  changeValue('LOAD_UNLOAD_SPEED', value === '' ? undefined : value, 'unloadSpeed')
                  setEditMode(true)
                } else {
                  changeValue('LOAD_UNLOAD_SPEED', value, 'unloadSpeed')
                  setEditMode(true)
                }
              }}
            />
          </div>
        ) : (
          <></>
        )
      case PlantParameter.EFFICIENCY:
        return <></>
      case PlantParameter.RGU_GROUP:
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <TextField
                disabled={!isEditPlant || !editModeRole || isPastDate}
                className={cls.LimitCell}
                label='Номер группы'
                type='number'
                positiveNumber
                value={item?.parameterValue?.value?.value && (item?.parameterValue?.value?.value as unknown as string)}
                onChange={(e) => {
                  const value = e.target.value
                  if (Number(value) >= 0) {
                    changeValue('RGU_GROUP', e.target.value, '')
                    setEditMode(true)
                  }
                }}
              />
            )}
          </>
        )
      case PlantParameter.RESTRICTION_CORRIDOR:
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <RestrictionCorridor
                item={
                  // Некорректная типизация метода, из-за этого пришлось добавить приведение типов
                  item as unknown as IParametersConfigsOutput<IToggleableRestrictionCorridorParameter['value']['value']>
                }
                isEditPlant={isEditPlant}
                editModeRole={editModeRole}
                isPastDate={isPastDate}
                changeValue={changeValue}
                setEditMode={setEditMode}
              />
            )}
          </>
        )
      default:
        return <></>
    }
  }

  return (
    <>
      <div className={cls.Extra}>
        {paramsFinal.map((el) => {
          const turnedOn = el?.parameterValue?.value?.turnedOn ?? false
          const isGenerator = el.parameterName === PlantParameter.GENERATOR_JOINT_WORK_WATCH
          const isPressure =
            el.parameterName === PlantParameter.PRESSURE_RECESSION_WATCH ||
            el.parameterName === PlantParameter.LOAD_UNLOAD_SPEED
          const isRGUMaxMin =
            el.parameterName === PlantParameter.RGU_MINIMUM_LIMIT ||
            el.parameterName === PlantParameter.RGU_MAXIMUM_LIMIT

          return (
            <div
              key={`${el.plantId}_${el.parameterName}`}
              className={classNames(
                cls.ExtraRow,
                {
                  [cls.Pressure]: isPressure,
                  [cls.rguMaxMin]: isRGUMaxMin,
                  [cls.ExtraRowError]: errors.has(el.parameterName),
                },
                [],
              )}
            >
              <IconButton
                onClick={() => {
                  setIsHistoryModal(el as unknown as HistoryParamsModalProps['object'])
                }}
                className={classNames(
                  cls.ButtonInfo,
                  {
                    [cls.isPressureInformation]: isPressure,
                  },
                  [],
                )}
              >
                <Icon width={14} name='history' />
              </IconButton>
              <div
                title={
                  el.parameterName === 'E_MAX_E_MIN' && !optimization
                    ? 'Дельта Эмакс - Эмин может быть включена только для оптимизируемых ГЭС'
                    : ''
                }
              >
                <Switch
                  disabled={
                    !isEditPlant ||
                    isPastDate ||
                    !editModeRole ||
                    ((el.parameterName === 'E_MAX_E_MIN' || el.parameterName === 'RESTRICTION_CORRIDOR') &&
                      !optimization) ||
                    (isRGUMaxMin && adjustableUnit !== 'RGU')
                  }
                  onChange={(_: unknown, value) => {
                    setParamsFinal((prev) => {
                      return prev.map((item) => {
                        if (item.parameterName === el.parameterName) {
                          const { parameterValue } = item
                          if (el.parameterName === 'EFFICIENCY') {
                            return {
                              ...item,
                              parameterValue: {
                                ...parameterValue,
                                value: { ...parameterValue.value, turnedOn: value, value: '1' },
                              },
                              isEdit: true,
                            }
                          }

                          return {
                            ...item,
                            parameterValue: {
                              ...parameterValue,
                              value: { ...parameterValue.value, turnedOn: value },
                            },
                            isEdit: true,
                          }
                        }

                        return item
                      }) as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>[]
                    })
                    setEditMode(true)
                    errors.delete(el.parameterName)
                  }}
                  checked={turnedOn}
                />
              </div>
              <div
                className={classNames(cls.ButtonInfo, {
                  [cls.GroupsExtra]: isGenerator,
                  [cls.Pressure]: isPressure,
                })}
              >
                {el.description}
              </div>
              <div
                className={classNames(
                  `${cls.ExtraCell} ${el.parameterName === 'GENERATOR_JOINT_WORK_WATCH' ? cls.ExtraCellGroup : ''}`,
                  { [cls.ExtraPressure]: isPressure },
                )}
              >
                {getExtraContent(el.parameterName, el)}
              </div>
              {errors.has(el.parameterName) && (
                <Tooltip
                  title={errors.get(el.parameterName)}
                  className={classNames(cls.infoIcon, { [cls.generator]: isGenerator }, [])}
                >
                  <div>
                    <Icon name='information' width={20} />
                  </div>
                </Tooltip>
              )}
            </div>
          )
        })}
      </div>
      {isLimitModal && (
        <LimitModal
          object={isLimitModal}
          disabled={!isEditPlant || !editModeRole || isPastDate}
          onConfirm={(res) => {
            const type = isLimitModal.mode === 'min' ? 'MINIMUM_LIMIT' : 'MAXIMUM_LIMIT'
            const actualLimitTemp = res.filter((el) => el.active).map((el) => Number(el.limit))
            const getLimit = () => {
              if (type === 'MINIMUM_LIMIT') {
                return Math.max.apply(null, actualLimitTemp).toFixed(3)
              }
              if (type === 'MAXIMUM_LIMIT') {
                return Math.min.apply(null, actualLimitTemp).toFixed(3)
              }
            }
            const actualLimit = actualLimitTemp.length > 0 ? getLimit() : '0'
            setParamsFinal((prev) => {
              return prev.map((el) => {
                if (el.parameterName === type) {
                  return {
                    ...el,
                    isEdit: true,
                    parameterValue: {
                      ...el.parameterValue,
                      value: {
                        ...el.parameterValue.value,
                        value: {
                          ...el.parameterValue.value.value,
                          actualLimit,
                          restrictions: res,
                        },
                      },
                    },
                  }
                }

                return el
              }) as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>[]
            })
            setIsLimitModal(null)
            setEditMode(true)
          }}
          onClose={() => {
            setIsLimitModal(null)
          }}
        />
      )}
      {createGroup && (
        <AddGroupsModal
          object={createGroup as unknown as IObj}
          setObject={setCreateGroup}
          onClose={() => {
            setCreateGroup(null)
          }}
          onConfirm={(res: { type: string; value: string }) => {
            setParamsFinal((prev) => {
              return prev.map((el) => {
                if (el.parameterName === 'GENERATOR_JOINT_WORK_WATCH') {
                  const groups = [...el.parameterValue.value.value.groups]
                  if (res.type === 'edit') {
                    const index = groups.findIndex((item) => item.value === res.value)
                    groups.splice(index, 1, res as unknown as ExValueConfigsOutput)
                  } else groups.push(res as unknown as ExValueConfigsOutput)

                  return {
                    ...el,
                    isEdit: true,
                    parameterValue: {
                      ...el.parameterValue,
                      value: {
                        ...el.parameterValue.value,
                        value: { ...el.parameterValue.value.value, groups: groups },
                      },
                    },
                  }
                }

                return el
              })
            })
            setCreateGroup(null)
            setEditMode(true)
          }}
          ggs={ggsAdd as unknown as IObj[]}
        />
      )}
      {deleteGroup && (
        <DeleteModal
          onClose={() => {
            setDeleteGroup(null)
          }}
          item={deleteGroup as unknown as { label: string }}
          onConfirm={() => {
            setParamsFinal((prev) => {
              return prev.map((el: IParametersConfigsOutput<ValueConfigsOutputWithGroups>) => {
                if (el.parameterName === 'GENERATOR_JOINT_WORK_WATCH') {
                  return {
                    ...el,
                    isEdit: true,
                    parameterValue: {
                      ...el.parameterValue,
                      value: {
                        ...el.parameterValue.value,
                        value: {
                          ...el.parameterValue.value.value,
                          groups: el.parameterValue.value.value.groups.filter(
                            (item) => item.value !== deleteGroup.value,
                          ),
                        },
                      },
                    },
                  }
                }

                return el
              })
            })
            setDeleteGroup(null)
            setEditMode(true)
          }}
        />
      )}
    </>
  )
})
