import { IParametersConfigsOutput } from 'entities/api/calcModelPage.entities.ts'
import { IToggleableRguRestrictionLimitParameter, PlantParameter } from 'entities/shared/common.entities.ts'
import { IPrepareRgu } from 'stores/CalcModelStore'

import { RguLimitTableProps } from '../ui'
import { RguLimitRow } from './useRguLimitModalTable.ts'

/**
 * Проверка значений в зависимости от типа ограничения.
 * Для минимума: значение должно заполняться максимальным из выбранных значений соответствующей РГЕ.
 * Для максимума: значение должно заполняться минимальным из выбранных значением соответствующей РГЕ.
 * @param type - Тип ограничения (МАКСИМУМ или МИНИМУМ)
 * @param mappedLimit - Промежуточная запись, которая уже сохранена
 * @param newLimit - Очередная запись, которую следует проверить с сохраненной
 * @returns Флаг, который определяет доступен ли объект ограничения для изменений
 */
const shouldUpdateRguLimitRecord = (type: RguLimitTableProps['type'], mappedLimit: number, newLimit: number) =>
  (type === PlantParameter.RGU_MINIMUM_LIMIT && mappedLimit < newLimit) ||
  (type === PlantParameter.RGU_MAXIMUM_LIMIT && mappedLimit > newLimit)

/**
 *
 * @param type - Тип ограничения (МАКСИМУМ или МИНИМУМ)
 * @param item - Данные ограничений РГЕ, который получены с бека / подготовлены для отправки на бек
 * @param rgus - РГЕ по просматриваемой станции
 * @returns Массив строк, который принимает таблица TableV1
 */
export const prepareRguActualLimitsRowsFromRestrictions = (
  type: RguLimitTableProps['type'],
  item: IParametersConfigsOutput<IToggleableRguRestrictionLimitParameter['value']['value']>,
  rgus: IPrepareRgu[],
) => {
  const rows = new Map<number, RguLimitRow>()

  if (!item?.parameterValue?.value?.value?.restrictions) return Array.from(rows.values())

  item.parameterValue.value.value.restrictions
    .filter((restriction) => restriction.active)
    .forEach((restriction) => {
      const rgu = rgus.find((rgu) => rgu.id === restriction.rguId)
      const mappedRecord = rows.get(restriction.rguId)

      const isFirstRecord = rgu && !mappedRecord
      const shouldUpdateExistedRecord =
        rgu && mappedRecord && shouldUpdateRguLimitRecord(type, Number(mappedRecord.limit), restriction.limit)

      if (isFirstRecord || shouldUpdateExistedRecord) {
        rows.set(restriction.rguId, {
          tabId: restriction.code,
          active: restriction.active,
          rguItem: {
            label: rgu.name,
            value: rgu.id,
          },
          // не отображаемое поле
          restrictionItem: {
            label: String(restriction.code),
            value: restriction.code,
          },
          limit: String(restriction.limit),
        })
      }
    })

  return Array.from(rows.values())
}

/**
 *
 * @param type - Тип ограничения (МАКСИМУМ или МИНИМУМ)
 * @param restrictions - Ограничения РГЕ
 * @returns Объект, где ключом является rguId, а значением минимальное или максимальное РГЕ среди активных со схожим id
 */
export const prepareRguActualLimitsFromRestrictions = (
  type: RguLimitTableProps['type'],
  restrictions: IToggleableRguRestrictionLimitParameter['value']['value']['restrictions'],
) => {
  const rows = new Map<number, IToggleableRguRestrictionLimitParameter['value']['value']['restrictions'][0]>()

  if (!restrictions) return Array.from(rows.values())

  restrictions
    .filter((restriction) => restriction.active)
    .forEach((restriction) => {
      const mappedRecord = rows.get(restriction.rguId)

      const isFirstRecord = !mappedRecord
      const shouldUpdateExistedRecord =
        mappedRecord && shouldUpdateRguLimitRecord(type, Number(mappedRecord.limit), restriction.limit)

      if (isFirstRecord || shouldUpdateExistedRecord) {
        rows.set(restriction.rguId, restriction)
      }
    })

  return Array.from(rows.values())
}
