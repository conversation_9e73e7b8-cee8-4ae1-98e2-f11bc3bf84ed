import { IParametersConfigsOutput } from 'entities/api/calcModelPage.entities.ts'
import { IToggleableRguRestrictionLimitParameter } from 'entities/shared/common.entities.ts'

/**
 *
 * @param item - Данные ограничений РГЕ, который получены с бека / подготовлены для отправки на бек
 * @returns Массив строк, который принимает таблица TableV1
 */
export const convertRguActualLimitsToRows = (
  item: IParametersConfigsOutput<IToggleableRguRestrictionLimitParameter['value']['value']>,
) => {
  return (
    item.parameterValue.value?.value?.actualLimits?.map((restriction) => ({
      tabId: restriction.code,
      active: restriction.active,
      rguItem: {
        label: restriction.rguName,
        value: restriction.rguId,
      },
      restrictionItem: {
        label: restriction.name,
        value: restriction.code,
      },
      limit: String(restriction.limit),
    })) || []
  )
}
