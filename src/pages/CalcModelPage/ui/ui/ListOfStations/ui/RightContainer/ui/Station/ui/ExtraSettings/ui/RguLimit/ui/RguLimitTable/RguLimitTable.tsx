import { IParametersConfigsOutput } from 'entities/api/calcModelPage.entities.ts'
import { IToggleableRguRestrictionLimitParameter, PlantParameter } from 'entities/shared/common.entities.ts'
import { observer } from 'mobx-react'
import { FC, useMemo } from 'react'
import { useStore } from 'stores/useStore.ts'
import { TableV1 } from 'widgets/TableV1'

import { convertRguActualLimitsToRows, prepareRguActualLimitsRowsFromRestrictions, rguLimitColumns } from '../../lib'

export interface RguLimitTableProps {
  type: PlantParameter.RGU_MINIMUM_LIMIT | PlantParameter.RGU_MAXIMUM_LIMIT
  item: IParametersConfigsOutput<IToggleableRguRestrictionLimitParameter['value']['value']>
  hasChanges: boolean
}

export const RguLimitTable: FC<RguLimitTableProps> = observer((props) => {
  const { type, item, hasChanges } = props
  const { calcModelStore } = useStore()
  const { configs } = calcModelStore

  const rowsFromRestrictions = useMemo(
    () => prepareRguActualLimitsRowsFromRestrictions(type, item, configs),
    [type, item, configs],
  )

  const initialRowsFromActualLimits = useMemo(() => convertRguActualLimitsToRows(item), [item])

  if (!rowsFromRestrictions.length) return null

  return (
    <TableV1
      rowHeight={25}
      maxVisibleRows={5}
      columns={rguLimitColumns}
      rows={hasChanges ? rowsFromRestrictions : initialRowsFromActualLimits}
    />
  )
})
