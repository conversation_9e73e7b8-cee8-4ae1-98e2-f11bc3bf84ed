import { IParametersConfigsOutput } from 'entities/api/calcModelPage.entities.ts'
import {
  IToggleableRguRestrictionLimitParameter,
  IToggleableRguRestrictionLimitParameterValue,
  PlantParameter,
} from 'entities/shared/common.entities.ts'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import { type FC, useCallback, useMemo } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { Loader } from 'shared/ui'
import { Button } from 'shared/ui/Button'
import { Modal } from 'shared/ui/Modal'
import { Select } from 'shared/ui/Select'
import { TextField } from 'shared/ui/TextField'
import { useStore } from 'stores/useStore.ts'
import { TableV1 } from 'widgets/TableV1'

import { numberOption, RguLimitLoadStatus, rguLimitModalColumns, useRguLimitModalTable } from '../../lib'
import cls from './RguLimitModal.module.scss'

const accessRoles = [ROLES.TECH_ADMIN_CM]

export interface RguLimitModalProps {
  type: PlantParameter.RGU_MINIMUM_LIMIT | PlantParameter.RGU_MAXIMUM_LIMIT
  item: IParametersConfigsOutput<IToggleableRguRestrictionLimitParameter['value']['value']>
  disabled: boolean
  onClose?: () => void
  onConfirm?: (res: IToggleableRguRestrictionLimitParameterValue[]) => void
}

export const RguLimitModal: FC<RguLimitModalProps> = observer((props) => {
  const { type, item, disabled, onClose, onConfirm } = props
  const { authStore } = useStore()
  const { userDetail } = authStore

  const {
    rows,
    selectedIds,
    setSelectedIds,
    selectedRguId,
    setSelectedRguId,
    selectedRestrictionCode,
    setSelectedRestrictionCode,
    limit,
    setLimit,
    hasChanges,
    rgusItemsForFilter,
    initialDataLoaded,
    restrictionItems,
    rguItems,
    canAddRestriction,
    handleReset: resetTable,
    handleAddRestriction,
    handleChangeLimit,
    handleDeleteRestriction,
    handleSave: saveTable,
    handleFilter: filterTable,
  } = useRguLimitModalTable(type, item)

  const hasEditAccess = useMemo(
    () =>
      userDetail.roles
        ? userDetail.roles.map((role) => role.role).some((userRole) => accessRoles.includes(userRole))
        : false,
    [userDetail.roles],
  )

  const handleSave = useCallback(() => {
    saveTable(onConfirm)
  }, [saveTable, onConfirm])

  const handleCloseModal = useCallback(() => {
    if (hasChanges) {
      const confirmed = window.confirm('Изменения сохранены не будут. Вы действительно хотите закрыть модальное окно?')
      if (confirmed) {
        onClose?.()
        resetTable()
      }
    } else {
      onClose?.()
      resetTable()
    }
  }, [hasChanges, onClose])

  useHotkeys('ctrl+shift+s', () => hasChanges && !disabled && handleSave())
  useHotkeys('ctrl+shift+x', () => hasChanges && !disabled && resetTable())

  return (
    <Modal
      title='Список ограничений РГЕ'
      onClose={handleCloseModal}
      maxWidth='lg'
      skipConfirmOnClose
      actions={
        hasEditAccess && (
          <div className={cls.buttons}>
            {hasChanges && !disabled && (
              <>
                <Button className={cls.button} variant='outlined' onClick={resetTable}>
                  Сбросить
                </Button>
                <Button className={cls.button} onClick={handleSave}>
                  Сохранить
                </Button>
              </>
            )}
          </div>
        )
      }
    >
      <div className={cls.bodyModal}>
        {/* Отображаем лоудер поверх остальных компонентов, чтобы форма рендерилась в фоне */}
        {RguLimitLoadStatus.loading === initialDataLoaded && (
          <div className={cls.loader}>
            <Loader />
          </div>
        )}
        <div className={cls.descriptionModal}>{type === 'RGU_MINIMUM_LIMIT' ? 'Минимум' : 'Максимум'}</div>
        <div className={cls.filterRow}>
          {hasEditAccess && (
            <>
              <Select
                label='РГЕ'
                className={cls.rguSelected}
                variant='outlined'
                items={rguItems}
                onChange={setSelectedRguId}
                disabled={disabled}
                value={selectedRguId ?? ' '}
              />
              <Select
                label='Причина'
                className={cls.limitSelected}
                variant='outlined'
                items={restrictionItems}
                onChange={setSelectedRestrictionCode}
                disabled={disabled}
                value={selectedRestrictionCode ?? ' '}
              />
              <TextField
                value={limit}
                type='number'
                disabled={disabled}
                numberOption={numberOption}
                onChange={(e) => setLimit(e.target.value)}
                label='МВт'
                className={cls.textField}
                toFixed={3}
              />
              <Button onClick={handleAddRestriction} disabled={!canAddRestriction || disabled}>
                Добавить
              </Button>
            </>
          )}
        </div>
        <TableV1
          rowHeight={25}
          maxVisibleRows={12}
          rows={rows}
          enabledSelectMode
          editMode={hasEditAccess && !disabled}
          columns={rguLimitModalColumns(
            hasEditAccess,
            disabled,
            rgusItemsForFilter,
            handleDeleteRestriction,
            handleChangeLimit,
          )}
          selection={selectedIds}
          setSelection={setSelectedIds}
          getRowId={(row) => row.tabId}
          onFilteringChange={filterTable}
        />
      </div>
    </Modal>
  )
})
