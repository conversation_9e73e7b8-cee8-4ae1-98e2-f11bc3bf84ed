import { type IColumn } from 'widgets/TableV1'

import { RguLimitRow } from './useRguLimitModalTable.ts'

/**
 * Массив колонок, который используется для отображения в Расчётной модели для ограничений максимума/минимума РГЕ
 */
export const rguLimitColumns: IColumn<RguLimitRow>[] = [
  {
    name: 'rguItem',
    title: 'РГЕ',
    width: 190,
    render: (_, row) => row.rguItem.label,
  },
  {
    name: 'restrictionItem',
    title: 'Значение, МВт',
    width: 110,
    render: (_, row) => row.limit,
  },
]
