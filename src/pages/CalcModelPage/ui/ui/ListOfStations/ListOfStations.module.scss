.ListOfStations {
  width: 100%;
  height: 100%;
  display: flex;
  background-color: var(--background-color-primary);
  overflow: hidden;
}

.Left {
  flex-shrink: 0;
  height: 100%;
  width: 14rem;
  min-width: 14rem;
  max-width: 14rem;
  background-color: var(--background-color-secondary);
  border-radius: 8px;
  box-shadow:
    0 2px 3px 0 rgb(0 0 0 / 5%),
    0 1px 2px 0 rgb(0 0 0 / 10%);
  padding: 0.2rem;
}

.Right {
  height: 100%;
  width: 100%;
  margin-left: 0.2rem;
  background-color: var(--background-color-secondary);
  border-radius: 8px;
  box-shadow:
    0 2px 3px 0 rgb(0 0 0 / 5%),
    0 1px 2px 0 rgb(0 0 0 / 10%);
  display: flex;
}

.LoaderContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.DatePicker {
  width: 130px;
  height: 24px;
  border-radius: 4px;

  & > div {
    height: 26px;
  }
}

.ButtonSettingStation {
  margin-top: 6px !important;
  height: 26px;
}

.HeaderLeft {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  width: 100%;
}

.SearchContainer {
  display: flex;
  margin: 16px 8px 10px;
}

.SearchInput {
  height: 18px;
  width: 100%;

  & > div > input {
    padding: 0 !important;
  }
}

.Buttons {
  margin-left: auto;
  width: 60px;
}

.SelectSort {
  color: var(--primary-color) !important;
}

.UnSelectSort {
  color: var(--text-gray) !important;
}

.DragAndDropContainer {
  width: 100%;
  height: 85%;
  overflow: auto;
}

.SearchButton {
  height: 22px;
  width: 22px !important;
  min-width: 22px !important;
  padding: 0 !important;
}

.IconContainer {
  margin: 0 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 22px !important;
  width: 22px !important;
  min-width: 22px !important;
  padding: 0 !important;
  color: var(--text-gray) !important;
}

.LabelDragMenu {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 214px;
}

.DragMenuCell {
  display: flex;
  overflow: hidden;
  width: 100%;
}

.Action {
  height: 100%;
  width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-gray) !important;
  margin-left: auto;
  margin-right: 4px;
}

.CustomLabel {
  font-size: 14px;
  max-width: 150px;
  display: flex;
  align-items: center;

  div {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
}

.Settings {
  color: var(--text-color) !important;
}

.Trash {
  color: var(--red-color) !important;
}

.View {
  color: var(--text-gray) !important;
}

.inactive {
  color: var(--text-gray) !important;
  font-style: italic;
  padding-right: 2px;
}

.Select {
  color: var(--primary-color) !important;
}

.HeaderRight {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 40px;
  padding: 0.5em 1em;
  background-color: var(--background-color-primary);
  border-radius: 8px;
  color: var(--text-color);

  @media (width <= 1200px) {
    div[class$='MuiToggleButtonGroup-root'] {
      flex-direction: column;
    }
  }
}

.HeaderTitle {
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  color: var(--text-color);
}

.Actions {
  width: 220px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.NoData {
  background-color: var(--background-color-secondary);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  color: var(--text-gray);
}

.DataContainer {
  width: 100%;
  height: 100%;
  padding: 0.2rem;
  display: flex;
  flex-direction: column;
}

.Efficiency {
  height: 36px;
  width: 100%;

  input {
    padding: 0 !important;
    height: 36px;
    width: 100%;
  }
}

.Cell {
  display: flex;
  align-items: center;
  flex-direction: row;
  width: 20%;
}

.Table {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.Extra {
  width: 100%;
  height: 100%;
  color: var(--text-color);
  overflow: auto;
  font-size: 14px;
}

.SwitchEmptyParams {
  width: 58px;
  height: 38px;
  margin-right: 8px;
}

.ButtonHistory {
  color: var(--text-gray) !important;
}

.ButtonInfo {
  min-width: 20px !important;
  min-height: 20px !important;
  border-radius: 50% !important;
  padding: 0 !important;
  margin-right: 10px !important;
  height: 24px;
}

.HiddenButton {
  visibility: hidden;
}

.ExtraRow {
  position: relative;
  display: grid;
  width: 100%;
  min-width: max-content;
  grid-template-columns: 20px 50px 260px 1fr 20px;
  grid-gap: 0 0.45em;
  padding-left: 20px;
  padding-right: 20px;
  align-items: center;
  justify-content: flex-start;
  margin: 0.6em 0;

  &Error {
    background-color: #d0141410;
    border-radius: 5px;
  }
}

.GroupsExtra {
  height: fit-content;
  align-items: flex-start;
  display: flex;
  flex-direction: column;
}

.isGenerator {
  margin-top: 9px !important;
}

.isPressureInformation {
}

.rguMaxMin {
  align-items: flex-start;
}

.Pressure {
  height: 120px;
  align-items: flex-start;
}

.generator {
  grid-row-end: 1;
  grid-column-end: 6;
}

.AddButton {
  margin-left: 240px;
  margin-bottom: 4px;
}

.ExtraCell {
  width: 300px;
}

.ExtraCellGroup {
  grid-column-start: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  width: fit-content;
  max-width: 500px;
  margin-bottom: 10px;
}

.ExtraPressure {
  height: 120px;
}

.FloodCell {
  input {
    min-width: 100px;
    padding: 4px 16px !important;
  }
}
.EminMax {
  input {
    min-width: 170px;
    padding: 4px 16px !important;
  }
}
.LimitCell {
  input {
    min-width: 145px;
    padding: 4px 16px !important;
  }
}

.OtherCell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.FLOOD_CONTAINER {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-around;
}

.LOAD_UNLOAD_SPEED {
  width: 230px;
}

.ExtraButton {
  white-space: nowrap;
  padding: 0 10px;
  width: 300px;
  margin-left: 10px;
}

.ExtraLabel {
  width: 260px;
}

.PressureLabel {
  width: 260px;
  margin-top: 7px;
}

.Voltage {
  width: 120px;

  & > div > div {
    padding: 4px 16px !important;
  }
}

.Ter {
  width: 300px;

  & > div > div {
  }
}

.Row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: var(--text-color);
  padding: 0.5em 1em;
  width: 100%;

  &:first-child {
    margin-top: 1.75em;
  }

  &:last-child {
    margin-bottom: 1.75em;
  }
}

.NameConfig {
  display: flex;
  align-items: center;
}

.IconConfig {
  color: var(--primary-color);
}

.infoIcon {
  top: 2px;
  right: 5px;
  color: var(--red-color);
}

.ButtonHistoryForRegular {
  color: var(--text-gray) !important;
  min-width: 20px !important;
  min-height: 20px !important;
  border-radius: 50% !important;
  padding: 0 !important;
  margin-right: 10px !important;
}

.ActionCell {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ActionHeader {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.DisabledIcon {
}
