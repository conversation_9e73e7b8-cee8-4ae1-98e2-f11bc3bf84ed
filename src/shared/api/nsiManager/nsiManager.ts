import {
  getLastTaskSK11TaskType,
  IEditEnergyDistrictOutput,
  IEditRestrictionCausesOutput,
  IGetCascades,
  IGetEnergyDistrictOutput,
  IGetLastTaskSK11Output,
  IGetLastTaskSK11OutputSync,
  IGetRegistryOutput,
  IGetRestrictionCauseOutput,
  IGetStationSK11Output,
  ILoadSK11ReferenceData,
  IMaxCode,
  ISaveRegistry,
  ISaveStationSK11Input,
  ISaveStationSK11Output,
  IStartSK11Output,
  IValidateSyncInput,
  IValidateSyncOutput,
  TGetRegistryInput,
} from 'entities/api/nsiManager.entities.ts'
import {
  IGetReferenceDataParams,
  IIndicatorObjDto,
  IIndicatorUpdateParams,
  IReferenceData,
  TIndicatorDto,
} from 'entities/pages/nsiPage.entities'
import { TActions } from 'entities/shared/common.entities.ts'
import qs from 'qs'
import { axiosInstance as api } from 'shared/lib/axios'
import mockRegistry from "./data.json";

export const getRegistry = (hierarchyType: TGetRegistryInput): Promise<IGetRegistryOutput[]> => {
  // return api.get(`/api/v1/registry?hierarchyType=${hierarchyType}`)
  return Promise.resolve(mockRegistry as IGetRegistryOutput[])
}

export const getActiveDepartments = (): Promise<IGetRegistryOutput[]> => {
  return api.get(`/api/v1/registry/departments`)
}

export const saveRegistry = (
  elements: ISaveRegistry[],
  hierarchyType: TGetRegistryInput,
): Promise<IGetRegistryOutput[]> => {
  return api.put(`/api/v1/registry?hierarchyType=${hierarchyType}`, {
    elements,
  })
}

export const getStationSK11 = (): Promise<IGetStationSK11Output> => {
  return api.post(`/api/v1/sk-11/plants/diff`, {}, {})
}

export const getLastTaskSK11 = (taskType: getLastTaskSK11TaskType) => {
  if (taskType === 'SYNC_REGISTRY') {
    return api.get<unknown, IGetLastTaskSK11OutputSync>(`/api/v1/sk-11/registry/status`)
  }

  return api.get<unknown, IGetLastTaskSK11Output>(`/api/v1/tasks/last?taskType=${taskType}`)
}

export const saveStationSK11 = (
  objectPost: ISaveStationSK11Input,
  abortSignal: AbortSignal,
): Promise<ISaveStationSK11Output> => {
  return api.put(`/api/v1/sk-11/plants`, objectPost, {
    signal: abortSignal,
  })
}

export const startSK11 = (): Promise<IStartSK11Output> => {
  return api.post(`/api/v1/sk-11/registry`)
}

export const loadReferenceDataSK11 = (params: { loadDate: string }): Promise<ILoadSK11ReferenceData> => {
  return api.post(`/api/v1/sk-11/reference-data`, undefined, { params })
}

export type taskTypes = 'SYNC_REGISTRY' | 'COMPARE_PLANTS' | 'APPLY_PLANTS' | 'EDIT_REGISTRY'
export interface IRegistryProtocolParams {
  fromDate: Date
  toDate: Date
  taskTypes?: taskTypes[]
  offset?: number
  limit?: number
}

export interface IRegistryProtocolTask {
  id: number
  type: {
    code: string
    title: string
  }
  status: string
  userFio: string
  createdDate: string
  updatedDate: string
  attemptCount: number
  resultMessage: string
}
export interface ResultType {
  type: string
  added: number
  changed: number
  deleted: number
  warnings: string[]
}
export interface IRegistryProtocolResponse {
  id?: string
  uuid?: string
  task: IRegistryProtocolTask
  modelVersion: number
  results: ResultType[]
  warnings: string[]
  errors: string[]
  updatedDate: string
}

export const getRegistryProtocol = (params: IRegistryProtocolParams): Promise<IRegistryProtocolResponse[]> =>
  api.get(`/api/v1/registry/protocol`, {
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' })
    },
  })

export interface IRegistryEventsParams {
  fromDate: Date | null
  toDate: Date | null
  registryEventType?: string
  registryTypes?: string[]
  departmentLike?: string
  nameLike?: string
  plantLike?: string
  parameterLike?: string
  uidLike?: string
  actorLike?: string
  offset?: number
  limit?: number
}

export interface IRegistryEventsResponse {
  eventDate: string
  eventType: string
  registryType: {
    code: string
    name: string
  }
  uid: string
  name: string
  departmentName: string
  plantName: string
  parameter: string
  oldValue: string
  newValue: string
  actor: string
}

export const getRegistryEvents = (params: IRegistryEventsParams): Promise<IRegistryEventsResponse[]> =>
  api.get(`/api/v1/registry/events`, {
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' })
    },
  })

// Перечень ограничений

interface RestrictionCausesParams {
  type?: 'MAXIMUM' | 'MINIMUM'
}
export const getRestrictionCauses = (
  params: RestrictionCausesParams,
): Promise<{ causes: IGetRestrictionCauseOutput[]; maxCode: IMaxCode }> =>
  api.get('/api/v1/restriction/causes', { params })

export interface RestrictionCausesEditParamsRestriction {
  action: string
  restriction: {
    type: string
    code?: number
    name: string
    allowArchive: boolean
    allowDelete: boolean
  }
}

export interface RestrictionCausesEditParams {
  restrictions: RestrictionCausesEditParamsRestriction[]
}

export const editRestrictionCauses = (
  params: RestrictionCausesEditParams,
): Promise<{ causes?: IEditRestrictionCausesOutput[] }> => api.put('/api/v1/restriction/causes', params)

// Перечень территорий/энергорайонов

export const getEnergyDistrict = (archived?: boolean): Promise<IGetEnergyDistrictOutput[]> =>
  api.get('/api/v1/energy-district', {
    params: {
      archived,
    },
  })

export interface EnergyDistrictParams {
  districts: Array<{
    action?: string
    district: {
      id?: number
      ispId: number
      name: string
      allowArchive: boolean
      allowDelete: boolean
      archived?: boolean
    }
  }>
}
// export const editEnergyDistrict = async (params: EnergyDistrictParams): Promise<{districts:IEditEnergyDistrictOutput[]}> => await api.put('/api/v1/energy-district', params)
export const editEnergyDistrict = (params: EnergyDistrictParams): Promise<IEditEnergyDistrictOutput[]> =>
  api.put('/api/v1/energy-district', params)

export const validateSync = (params: IValidateSyncInput, abortSignal: AbortSignal): Promise<IValidateSyncOutput> =>
  api.post('/api/v1/registry/validate-sync', params, {
    signal: abortSignal,
  })

export const getCascades = (showArchived: boolean, activeOnly: boolean): Promise<IGetCascades[]> => {
  return api.get(`/api/v1/cascades?showArchived=${showArchived}&activeOnly=${activeOnly}`, {})
}

export interface ICascadesPost {
  action: TActions
  id: number | null
  name: string
}

export const saveCascades = (
  cascades: ICascadesPost[],
  showArchived: boolean,
  activeOnly: boolean,
): Promise<IGetCascades[]> => {
  return api.put(`/api/v1/cascades`, { cascades }, { params: { showArchived, activeOnly } })
}

export const getReferenceData = (params: IGetReferenceDataParams, signal?: AbortSignal): Promise<IReferenceData> =>
  api.get(`/api/v1/plant/reference-data`, { params, signal })

export const getIndicators = (): Promise<IIndicatorObjDto[]> => api.get(`/api/v1/indicator`)

export const updateIndicator = (data: IIndicatorUpdateParams): Promise<TIndicatorDto> =>
  api.put(`/api/v1/indicator`, data)
