import {
  IToggleableRguRestrictionLimitParameterValue,
  PlantParameter,
  TActions,
} from 'entities/shared/common.entities.ts'

export type TParameterName =
  | 'REGULATED_UNIT'
  | 'OPTIMIZATION'
  | 'EFFICIENCY'
  | 'CONSUMPTION_SCHEDULE_BINDING'
  | 'PARTICIPATION_NPRCH'
  | 'PARTICIPATION_AVRCHM'
  | 'GENERATOR_JOINT_WORK_WATCH'
  | 'MINIMUM_LIMIT'
  | 'MAXIMUM_LIMIT'
  | 'PRESSURE_RECESSION_WATCH'
  | 'RGU_GROUP'
  | 'E_MAX_E_MIN'
  | 'LOAD_UNLOAD_SPEED'
  | 'FLOOD_MODE_WATCH'
  | 'PRIORITY_LOAD'
  | 'RESTRICTION_CORRIDOR'

export interface getHistoryWriteModesParams {
  plantId: number
  calcDate: string
  planingStage: string
}

export interface writePlanRguDataParams {
  plantId: number
  targetDate: string
  planingStage: string
}

export interface IWritePlanRguDataOutput {
  id: number
  type: {
    code: string
    title: string
  }
  status: 'IN_PROCESS' | 'FAILED' | 'DONE'
  userFio: string
  createdDate: Date
  updatedDate: Date
  attemptCount: number
  resultMessage: string
  params: unknown
}

export interface GeneralParameter {
  order?: number
  stage: {
    code: string
    title: string
  }
  category: {
    code: 'VSVGO' | 'PSV' | 'PSV'
    title: string
  }
  endTime?: string
  offsetDays: number
  active: boolean
}

export interface IGetEnergyDistrictForParams {
  id: number
  ispId: number
  name: string
  used: boolean
  archived: boolean
}

export interface getDepPlantsOutput {
  type: 'DEPARTMENT' | 'PLANT'
  id: number
  name: string
  children: getDepPlantsOutput[]
  isLooked: boolean
  isPlanned: boolean
}

export type IWerRestriction = {
  id: number
  plantId: number
  parameter: {
    code: string
    title: string
  }
  category: {
    code: string
    title: string
  }
  type: {
    code: string
    title: string
  }
  // --06-12
  beginDate: string | null
  // --06-20
  stopDate: string | null
  // 2025-04-11
  temporaryBeginDate: string | null
  // 2025-04-11
  temporaryEndDate: string | null
  minValue: number | null
  maxValue: number | null
  valueType: {
    code: string
    title: string
  } | null
  comment: string | null
}

export interface IParameter {
  code: string
  title: string
}

export interface IWerRestrictionOptionsOutput {
  parameters: IParameter[]
  categories: IParameter[]
  types: IParameter[]
  valueTypes: IParameter[]
}

interface ISelectItem {
  value: string
  label: string
}

export interface IWerRestrictionOptions {
  parameters: ISelectItem[]
  categories: ISelectItem[]
  types: ISelectItem[]
  valueTypes: ISelectItem[]
}

export interface IWerRestrictionInput {
  restrictions: {
    action: TActions
    restriction: Omit<IWerRestriction, 'id' | 'startDate' | 'cancelDate'>
  }[]
}

export interface IGetConsumptionSchedulesOutput {
  id: number
  energyDistrictIspId: number
  energyDistrictName: string
  notation: string
  plants: [
    {
      plantId: number
      name: string
    },
  ]
  canDelete: boolean
}

export interface IGenerators {
  id: number
  name: string
  startDate: string //YYYY-MM-DD
  endDate: string
  marketCalcModelId: number
  activateDate: string
  deactivateDate: string
}

export interface IRGUS {
  id: number
  name: string
  startDate: string
  endDate: string
  marketCalcModelId: number
  generators: IGenerators[]
  children?: IRGUS[]
  parameters: IParameterItem[]
}

export interface ExValueConfigsOutput {
  ids: string[]
  value: string
  childs: ExValueConfigsOutput[]
  name: string
  id: string
  label?: string
}

export interface IRestriction {
  tabId: string
  active: boolean
  disabledChecked: boolean
  visibleAndDisabled: boolean
}

export type ValueConfigsOutputWithGroups = {
  groups: ExValueConfigsOutput[]
  actualLimit?: string
  restrictions: IRestriction[]
  lossPortionRatio?: string
  generatorPower?: string
  loadSpeed?: string
  unloadSpeed?: string
  name?: string
  residualOutput?: string
  id?: number
}

export type ValueConfigsOutput =
  | number
  | string
  | ValueConfigsOutputWithGroups
  | undefined
  | IToggleableRguRestrictionLimitParameterValue[]

export interface IParametersConfigsOutput<T> {
  plantId: number
  parameterName: PlantParameter
  description: string
  startDate: string
  priority: number
  isEdit?: boolean
  parameterValue: {
    type: boolean
    value: {
      turnedOn: boolean
      value: T
    }
  }
}

export interface IGetConfigsOutput {
  id: number
  name: string
  type: 'GES' | 'GAES'
  startDate: string
  endDate: string
  marketCalcModelId: number
  rgus: IRGUS[]
  parameters: IParametersConfigsOutput<ValueConfigsOutputWithGroups | number | string>[]
}

export interface ISaveDepPlantsInput {
  addLookPlantIds: number[]
  deleteLookPlantIds: number[]
}

export interface IGetRecordModesForIdOutput {
  plantId: number
  departmentId: number
  record: boolean
}

interface IParameterItem {
  parameterName: string
  parameterValue: {
    type: string
    value: object
  }
}
interface IRgusAndGeneratorsItem {
  id: number
  parameters: IParameterItem[]
}

export interface ISaveParamsInput {
  id: number
  parameters: IParameterItem[]
  rgus: IRgusAndGeneratorsItem[]
  generators: IRgusAndGeneratorsItem[]
}
export interface IGetRestrictionItemInput {
  type: string
  code: number
  name: string
  used: boolean
  archived: boolean
}

export interface IGetAcceptHistoryParamsOutput {
  updatedDate: Date
  accepted: boolean
  userName: string
  departmentName: string
}

export interface IGetHistoryParamsOutput {
  plantId: number
  parameterName: TParameterName
  description: string
  startDate: string
  endDate: string
  parameterValue: {
    type: string
    value: object
  }
}

export interface IGetActiveStagesItemOutput {
  code?: string
  title: string
  label?: string
  value?: number | string
}

export interface IGetConsumptionFormulasItemOutput {
  planingStage: {
    code: string
    title: string
  }
  planingStageIsp: {
    code: string
    title: string
  }
  departmentLevel: {
    code: string
    title: string
  }
  active: boolean
  formula: string
}

interface consumptionFormulaItem {
  planingStage: string
  planingStageIsp: string
  departmentLevel: string
}

export interface IAddConsumptionSchedulesInput {
  energyDistrictId?: number
  notation?: string
  consumptionFormulaList?: consumptionFormulaItem[]
}

export interface IGetAVRCHMSpreadsheetCell {
  value?: number | string
  date?: string
  hour?: number
  column?: string
  editable?: boolean
  manual?: boolean
  fixed?: boolean
  input?: string
  plantId?: number
  floodMode?: boolean
}

export interface IAVRCHMSpreadsheetNestedHeaderData {
  accepted?: boolean
}
