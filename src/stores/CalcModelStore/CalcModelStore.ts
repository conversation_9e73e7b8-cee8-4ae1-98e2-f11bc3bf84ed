import { addDays, format, isAfter, startOfDay } from 'date-fns'
import {
  ExValueConfigsOutput,
  getDepPlantsOutput,
  IAddConsumptionSchedulesInput,
  IGenerators,
  IGetAcceptHistoryParamsOutput,
  IGetActiveStagesItemOutput,
  IGetConsumptionSchedulesOutput,
  IGetEnergyDistrictForParams,
  IGetHistoryParamsOutput,
  IGetRestrictionItemInput,
  IParametersConfigsOutput,
  IRGUS,
  ISaveDepPlantsInput,
  ISaveParamsInput,
  TParameterName,
  ValueConfigsOutputWithGroups,
} from 'entities/api/calcModelPage.entities.ts'
import { PlanningStage, PlantParameter } from 'entities/shared/common.entities.ts'
import { ROLES } from 'entities/shared/roles.entities'
import { makeAutoObservable, runInAction } from 'mobx'
import api from 'shared/api/index'
import { IStageType } from 'shared/api/reportsManager/reportsManager'
import { getDateByUrlOrNextDateAfterCurrentByCurrentRoute } from 'shared/lib/dateFormates'
import { enrichArrayWithTabId } from 'shared/lib/enrichArrayWithTabId'
import { generateUUID } from 'shared/lib/GenerationUUID'
import { locationParse } from 'shared/lib/locationParse'
import { prepareDate } from 'shared/lib/prepareData'
import { IconNameProps } from 'shared/ui/Icon/Icon.type.ts'
import { ListOfStationsStore } from 'stores/CalcModelStore/ListOfStationsStore'
import {
  ICalcModelPlant,
  IGetEnergyDistrictOutput,
  StagesProps,
} from 'stores/CalcModelStore/ListOfStationsStore/ListOfStationsStore.types'
import { type RootStore } from 'stores/RootStore'

const ACTUAL_ITEM = {
  value: 'ACTUAL',
  label: 'Актуальный',
  color: '#000000',
}

const CALCULATION_MODEL_FIELDS_PRIORITY = [
  // Основные характеристики ГЭС
  PlantParameter.REGULATED_UNIT,
  PlantParameter.OPTIMIZATION,
  // Дополнительные характеристики ГЭС
  PlantParameter.E_MAX_E_MIN,
  PlantParameter.P_GEN_DELTA,
  PlantParameter.TERTIARY_RESERVE,
  PlantParameter.CONSUMPTION_SCHEDULE_BINDING,
  PlantParameter.MINIMUM_LIMIT,
  PlantParameter.MAXIMUM_LIMIT,
  PlantParameter.RESTRICTION_CORRIDOR,
  PlantParameter.RGU_MINIMUM_LIMIT,
  PlantParameter.RGU_MAXIMUM_LIMIT,
  PlantParameter.RGU_GROUP,
  PlantParameter.PARTICIPATION_AVRCHM,
  PlantParameter.PARTICIPATION_NPRCH,
  PlantParameter.LOAD_UNLOAD_SPEED,
  PlantParameter.FLOOD_MODE_WATCH,
  // Основные характеристики ГАЭС
  PlantParameter.EFFICIENCY,
  // Дополнительные характеристики ГАЭС
  PlantParameter.GENERATOR_JOINT_WORK_WATCH,
  PlantParameter.PRESSURE_RECESSION_WATCH,
]

export interface IConsumptionSchedules extends IGetConsumptionSchedulesOutput {
  tabId: number
  tempEdits?: string[]
}

export interface IEnergyDistrictForParams extends IGetEnergyDistrictForParams {
  value: number
  label: string
}

export interface iPrepareDepPlants {
  id: number
  name: string
  type: 'PLANT' | 'RGU' | 'DEPARTMENT'
  isPlanned: boolean
  visibleAndDisabled: boolean
  isLooked: boolean
  disabledChecked: boolean
  children: iPrepareDepPlants[]
}

export interface IPrepareRguParameters {
  parameterName: string
  parameterValue: {
    type: string
    edited?: boolean
    value: {
      turnedOn: boolean
    }
  }
}

export interface IPrepareRgu {
  tabId?: string
  children?: IPrepareRgu[]
  parameters?: IPrepareRguParameters[]
  generators?: IPrepareRgu[]
  startDate?: string
  endDate?: string
  id: number
  name: string
  priorityLoad?: boolean
  avrchm?: boolean
  voltage?: string
  isEdit?: boolean
}

export interface IPrepareGroups {
  id?: number
  value: number
}

export interface IActiveStage extends IGetActiveStagesItemOutput {
  tabId: string
  title: string
  titleICP: string | number | null
  isEdit?: boolean
  dcLevel: string | number | null
  formula?: string | null
  rowColor?: string
  disabledEditColumns?: string[]
  tempEdits?: string[] //| number[]
  planingStage?: {
    code?: string
  }
}

export interface IDepPlants extends iPrepareDepPlants {
  tabId: string
  children: IDepPlants[]
  visibleAndDisabled: boolean
  disabledChecked: boolean
  rowColor: string
  prompt: string
}

export interface IRestriction extends IGetRestrictionItemInput {
  value?: number
  label: string
  icon?: 'color' | IconNameProps | 'domain'
  color?: string
}

export interface IPlantForLeftMenu extends ICalcModelPlant {
  value: number
  label: string
  icon?: 'view' | 'settings'
  order: number
}

export interface IStage extends Omit<Omit<StagesProps, 'title'>, 'code'> {
  value: PlanningStage | string
  label: string
  color: string
}

interface IWarningsAfterSaving {
  warnings: string[]
  requestParams: ISaveParamsInput
  date: string
}

export interface ISaveErrorResponse {
  rid: string
  status: number
  message: string
  details: string[]
}

export class CalcModelStore {
  rootStore: RootStore
  listOfStationsStore: ListOfStationsStore
  plants: IPlantForLeftMenu[] = []
  typeStation: 'GES' | 'GAES' | null = null
  depPlants: IDepPlants[] = []
  configs: IPrepareRgu[] = []
  params: IParametersConfigsOutput<number | string | ValueConfigsOutputWithGroups>[] = []
  history: IGetHistoryParamsOutput[] = []
  acceptHistory: IGetAcceptHistoryParamsOutput[] = []
  restriction: IRestriction[] = []
  activeStages: IActiveStage[] = []
  energyDistrict: IGetEnergyDistrictOutput[] = []
  energyDistrictForParams: IEnergyDistrictForParams[] = []
  allStages: IGetActiveStagesItemOutput[] = []
  stages: IStage[] = []
  actualStage?: IStageType
  selectedStage?: string
  departmentsLevel: IGetActiveStagesItemOutput[] = []
  consumptionSchedules: IConsumptionSchedules[] = []
  lookedPlants: string[] = []
  isLoadingPlants: boolean = false
  loadStages: boolean = false
  date: Date = getDateByUrlOrNextDateAfterCurrentByCurrentRoute('calcModel')
  typeSort: 'alphabeat' | 'custom'
  vaultData = []
  warningsAfterSaving: IWarningsAfterSaving | null = null

  constructor(rootStore: RootStore, listOfStationsStore: ListOfStationsStore) {
    this.rootStore = rootStore
    this.listOfStationsStore = listOfStationsStore
    makeAutoObservable(this)
    this.typeSort = 'custom'
    this.vaultData = []
  }

  resetStore() {
    this.date = addDays(new Date(), 1)
    this.typeSort = 'custom'
    this.listOfStationsStore.resetStore()
  }

  changeActualStage = (code: PlanningStage, title: string) => {
    this.actualStage = { code, title, finished: false }
  }

  changeDate(value: Date) {
    this.date = value
  }

  get formattedDate() {
    return format(this.date, 'yyyy-MM-dd')
  }

  get editMode() {
    const {
      userDetail: { roles },
    } = this.rootStore.authStore
    const accessRoles = [ROLES.TECH_ADMIN_CM]

    return roles.some(({ role }) => accessRoles.includes(role))
  }

  get isPastDate() {
    const today = startOfDay(new Date())
    const selectedDay = startOfDay(this.date)

    return !isAfter(selectedDay, today)
  }

  resetLookedPlants = () => {
    this.lookedPlants = []
  }

  changeSortType = async (sortByOrder: boolean, date: string, planingStage?: string | null | undefined) => {
    this.isLoadingPlants = true
    try {
      const plants = await api.calcModelManager.getPlanPlants({ date, sortByOrder, planingStage, showAll: true })
      runInAction(() => {
        this.plants = plants.map((el) => {
          // В связи с изменением API может вернуться объект без viewOnly
          const base = {
            ...el,
            value: el.plantId,
            label: el.name,
          }
          if ('viewOnly' in el) {
            return {
              ...base,
              icon: (el as ICalcModelPlant).viewOnly || !el.active ? 'view' : 'settings',
            }
          }

          return {
            ...base,
          }
        }) as IPlantForLeftMenu[]
      })
      if (sortByOrder) {
        this.typeSort = 'custom'
      } else {
        this.typeSort = 'alphabeat'
      }

      return this.plants
    } catch (e) {
      console.log(e)
    } finally {
      this.isLoadingPlants = false
    }
  }

  resetPlants = () => {
    runInAction(() => {
      this.plants = []
      this.configs = []
    })
  }

  getColorItem = (type: string) => {
    if (type.toUpperCase().includes('ВСВГО')) {
      return 'var(--color-stage-vsvgo)'
    }

    return 'var(--color-stage-rsv)'
  }

  setSelectedStage = (stage: string) => {
    this.selectedStage = stage
  }

  initStages = async (date: string) => {
    const { selectstage = null } = locationParse(location.search)

    this.loadStages = true
    try {
      const { actualStage, stages } = await api.calculationsManager.getStages({
        calcDate: date,
      })
      this.actualStage = actualStage
      this.stages = actualStage
        ? [
            ACTUAL_ITEM,
            ...stages.map((item) => {
              const color = this.getColorItem(item.title)

              return { value: item.code, label: item.title, color }
            }),
          ]
        : stages.map((item) => {
            const color = this.getColorItem(item.title)

            return { value: item.code, label: item.title, color }
          })
      const [first] = stages
      this.setSelectedStage(selectstage ? selectstage.toUpperCase() : actualStage ? ACTUAL_ITEM.value : first.code)
    } catch (e) {
      console.log(e)
    } finally {
      this.loadStages = false
    }
  }

  initListOfStations = async (date: string) => {
    try {
      this.initStages(date)
      const energyDistrictForParams: IGetEnergyDistrictForParams[] =
        await api.calcModelManager.getEnergyDistrictForParams()
      this.energyDistrictForParams = energyDistrictForParams.map((el) => ({
        ...el,
        value: el.id,
        label: el.name,
      }))
    } catch (e) {
      console.log(e)
    }
  }

  prepareDepPlants(arr: getDepPlantsOutput[]): IDepPlants[] {
    return arr.map((el) => {
      const disabledChecked = el.type !== 'PLANT'
      const visibleAndDisabled = el.isPlanned
      const children = el?.children?.length > 0 ? this.prepareDepPlants(el.children) : []
      const tabId = generateUUID()
      if (el.isLooked) this.lookedPlants.push(tabId)

      return {
        ...el,
        tabId,
        children,
        visibleAndDisabled,
        disabledChecked,
        rowColor: el.type === 'PLANT' && el.isPlanned ? 'gray' : '',
        prompt: el.type === 'PLANT' && el.isPlanned ? 'Планируемая' : '',
      }
    })
  }

  initModalAdd = async () => {
    try {
      const depPlants = await api.calcModelManager.getDepPlants()
      this.depPlants = this.prepareDepPlants(depPlants)
    } catch (e) {
      console.log(e)
    }
  }

  saveDepPlants = async (res: ISaveDepPlantsInput) => {
    try {
      await api.calcModelManager.saveDepPlants(res)
    } catch (e) {
      console.log(e)
    }
  }

  setCustomSortLeft = async (res: string[]) => {
    try {
      const final = res.map((el: string) => Number(el))
      await api.calcModelManager.saveCustomSort(final)
    } catch (e) {
      console.log(e)
    }
  }

  getModesRecForId = async (id: number) => {
    try {
      return await api.calcModelManager.getRecordModesForId(id)
    } catch (e) {
      console.log(e)
    }
  }

  getInMixingById = async (id: number) => {
    try {
      return await api.calcModelManager.getInMixingById({ plantId: id })
    } catch (e) {
      console.log(e)
    }
  }

  saveModesRecForId = async (plantId: number, isRecord: boolean) => {
    try {
      return await api.calcModelManager.saveRecordModesForId(plantId, isRecord)
    } catch (e) {
      console.log(e)
    }
  }

  editInMixingById = async (plantId: number, isMixing: boolean) => {
    try {
      return await api.calcModelManager.editInMixingById({
        plantId,
        mixing: isMixing,
      })
    } catch (e) {
      console.log(e)
    }
  }

  prepareRgu = (arr: IPrepareRgu[], isChild?: boolean): IPrepareRgu[] => {
    return arr.map((el) => {
      const voltage = el?.parameters?.find((par) => par.parameterName === 'VOLTAGE')?.parameterValue?.value ?? ''
      const avrchm = !isChild
        ? el?.generators?.some((el) =>
            el.parameters?.some(
              (par) => par.parameterName === 'PARTICIPATION_AVRCHM' && par.parameterValue?.value.turnedOn,
            ),
          )
        : el?.parameters?.find((par) => par.parameterName === 'PARTICIPATION_AVRCHM')?.parameterValue?.value.turnedOn
      const priorityLoad =
        el?.parameters?.find((item) => item.parameterName === 'PRIORITY_LOAD')?.parameterValue?.value?.turnedOn ?? false
      const children = el?.generators && el?.generators?.length > 0 ? this.prepareRgu(el.generators, true) : []
      const startDate = el.startDate ? prepareDate(el.startDate) : ''
      const endDate = el.endDate ? prepareDate(el.endDate) : ''
      const disabledEditColumns = el.generators ? [] : ['voltage']

      return {
        ...el,
        children,
        voltage,
        avrchm,
        priorityLoad,
        startDate,
        endDate,
        disabledEditColumns,
      }
    }) as IPrepareRgu[]
  }

  prepareGroups(arr: number[], flatData: { id: number }[]): IPrepareGroups[] {
    return arr.map((el) => {
      const find = flatData.find((item) => item.id === el) ?? {}

      return { ...find, value: el }
    })
  }

  getParams = async (plantId: number, startDate: string) => {
    try {
      if (plantId !== 0) {
        const { parameters, rgus, type } = await api.calcModelManager.getConfigs(plantId, startDate) // startDate
        this.params = parameters.map((el) => {
          const name = el.description
          const priority = CALCULATION_MODEL_FIELDS_PRIORITY.findIndex((fieldName) => fieldName === el.parameterName)
          if (el.parameterName === 'E_MAX_E_MIN') {
            return {
              ...el,
              name,
              priority,
              parameterValue: {
                ...el.parameterValue,
                value: {
                  ...el.parameterValue.value,
                  value:
                    typeof el?.parameterValue?.value?.value === 'number' ? el?.parameterValue?.value?.value / 1000 : 0,
                },
              },
            }
          }
          if (['RGU_GROUP', 'PARTICIPATION_AVRCHM', 'PARTICIPATION_NPRCH'].includes(el.parameterName)) {
            return {
              ...el,
              name,
              priority,
              parameterValue: {
                ...el.parameterValue,
                value: {
                  ...el.parameterValue.value,
                  value: el?.parameterValue?.value?.value ?? 0,
                },
              },
            }
          }
          if (
            el.parameterName === 'PRESSURE_RECESSION_WATCH' &&
            typeof el.parameterValue.value.value === 'object' &&
            el.parameterValue.value.value !== null
          ) {
            return {
              ...el,
              name,
              priority,
              parameterValue: {
                ...el.parameterValue,
                value: {
                  ...el.parameterValue.value,
                  value: {
                    ...el.parameterValue.value.value,
                    residualOutput: el?.parameterValue?.value?.value?.residualOutput ?? 0,
                    lossPortionRatio: el?.parameterValue?.value?.value?.lossPortionRatio ?? 0,
                    generatorPower: el?.parameterValue?.value?.value?.generatorPower ?? 0,
                  },
                },
              },
            }
          }
          if (el.parameterName === 'GENERATOR_JOINT_WORK_WATCH') {
            const flatData = rgus.reduce((acc: IGenerators[], el: IRGUS) => {
              return [...acc, ...el.generators]
            }, [])
            const res = el as IParametersConfigsOutput<ValueConfigsOutputWithGroups>
            const tempGroups = res?.parameterValue?.value?.value?.groups
            const resGroups = Array.isArray(tempGroups) ? tempGroups : []
            const groups = resGroups.map((group: ExValueConfigsOutput) => {
              const value = generateUUID()
              const childs = this.prepareGroups(group.ids as unknown as number[], flatData) ?? []

              return { ...group, value, childs }
            })

            return {
              ...res,
              name,
              priority,
              parameterValue: {
                ...res.parameterValue,
                value: {
                  ...res.parameterValue.value,
                  value: res?.parameterValue?.value?.value
                    ? {
                        ...res?.parameterValue?.value?.value,
                        groups,
                      }
                    : { groups },
                },
              },
            }
          }

          return { ...el, name, priority }
        }) as IParametersConfigsOutput<number | string | ValueConfigsOutputWithGroups>[]
        this.configs = this.prepareRgu(rgus as IPrepareRgu[])
        this.typeStation = type ?? null
      }
    } catch (e) {
      console.log(e)
    }
  }

  saveParams = async (res: ISaveParamsInput, date: string, forced: boolean | undefined = false) => {
    try {
      await api.calcModelManager.saveParams(res, date, forced)
      this.rootStore.notificationStore.addNotification({
        title: 'Сохранение РМ ',
        description: 'Параметры РМ сохранены',
        type: 'success',
      })
    } catch (e) {
      const error = e as ISaveErrorResponse
      if ((error.status === 409 || error.status === 400) && error.details) {
        this.warningsAfterSaving = {
          warnings: error.details,
          requestParams: res,
          date,
        }
      } else {
        console.log(e)
        this.warningsAfterSaving = null
      }
      throw error
    }
  }

  clearWarningsAfterSaving = () => {
    this.warningsAfterSaving = null
  }

  initLimits = async (type: string, archived: boolean | null) => {
    try {
      const TYPE_LIMITS = type === 'min' ? 'MINIMUM' : 'MAXIMUM'
      const { causes } = await api.calcModelManager.getRestriction(TYPE_LIMITS, archived)
      this.restriction = causes.map((el) => ({ ...el, label: el.name, value: el.code })).filter((el) => !el.archived)
    } catch (e) {
      console.log(e)
    }
  }

  initAcceptHistory = async (plantId: string, calcDate: string, planingStage: string) => {
    try {
      const history = await api.calcModelManager.getAcceptHistoryParams(plantId, calcDate, planingStage)
      runInAction(() => {
        this.acceptHistory = history
      })
    } catch (e) {
      console.log(e)
    }
  }

  initHistory = async (plantId: number, parameterName: TParameterName) => {
    try {
      const history = await api.calcModelManager.getHistoryParams(plantId, parameterName)
      this.history = enrichArrayWithTabId(history)
    } catch (e) {
      console.log(e)
    }
  }

  initHistoryGenerator = async (generatorId: number, parameterName: string) => {
    try {
      const history = await api.calcModelManager.getGeneratorParametersHistory(generatorId, parameterName)
      this.history = enrichArrayWithTabId(history)
    } catch (e) {
      console.log(e)
    }
  }

  initHistoryRgu = async (rguId: string, parameterName: string) => {
    try {
      const history = await api.calcModelManager.getRGUParametersHistory(rguId, parameterName)
      this.history = enrichArrayWithTabId(history)
    } catch (e) {
      console.log(e)
    }
  }

  intConsumptionSchedules = async () => {
    try {
      const consumptionSchedules = await api.calcModelManager.getConsumptionSchedules()
      this.consumptionSchedules = consumptionSchedules.map((el: IGetConsumptionSchedulesOutput) => ({
        ...el,
        tabId: el.energyDistrictIspId,
      }))
    } catch (e) {
      console.log(e)
    }
  }

  initAddConsumptionSchedules = async (type: 'add' | 'edit', id: number) => {
    try {
      const energyDistrict = await api.calcModelManager.getEnergyDistrict()
      this.energyDistrict = energyDistrict.map((el) => ({
        ...el,
        value: el.id,
        label: el.name,
      }))
      const allStages = await api.calcModelManager.getAllStages()
      this.allStages = allStages.map((el) => ({
        ...el,
        value: el.code,
        label: el.title,
      }))
      const departmentsLevel = await api.calcModelManager.getDepartmentsLevels()
      this.departmentsLevel = departmentsLevel.map((el, index) => ({
        ...el,
        value: index + 1,
        label: el.title,
      }))
      if (type === 'add') {
        const activeStages = await api.calcModelManager.getActiveStages()
        this.activeStages = activeStages.map((el) => ({
          ...el,
          tabId: generateUUID(),
          titleICP: el.code ?? null,
          dcLevel: null,
          formula: null,
        }))

        return this.activeStages
      } else {
        const find = this.energyDistrict?.find((el) => el.ispId === id)?.id ?? null
        const activeStages = await api.calcModelManager.getConsumptionFormulas(find)
        this.activeStages = activeStages.map((el) => {
          const dcLevel = this.departmentsLevel?.find((item) => item?.code === el?.departmentLevel?.code)?.value ?? ''
          const titleICP = this.allStages?.find((item) => item?.code === el?.planingStageIsp?.code)?.value ?? ''
          const formula = el.formula
          const disabledEditColumns = el.active ? [] : ['title', 'titleICP', 'dcLevel', 'formula']
          const rowColor = el.active ? 'white' : 'gray'

          return {
            ...el,
            tabId: generateUUID(),
            title: el?.planingStage?.title,
            titleICP: titleICP || el.planingStage?.code,
            dcLevel,
            formula,
            disabledEditColumns,
            rowColor,
          }
        })

        return this.activeStages
      }
    } catch (e) {
      console.log(e)
    }
  }

  addConsumptionSchedules = async (res: IAddConsumptionSchedulesInput) => {
    try {
      await api.calcModelManager.addConsumptionSchedules(res)
    } catch (e) {
      console.log(e)
    }
  }

  saveConsumptionSchedules = async (res: IAddConsumptionSchedulesInput) => {
    try {
      await api.calcModelManager.saveConsumptionSchedules(res)
    } catch (e) {
      console.log(e)
    }
  }

  trashConsumptionSchedules = async (consumptionScheduleId: string) => {
    try {
      await api.calcModelManager.trashConsumptionSchedules(consumptionScheduleId)
    } catch (e) {
      console.log(e)
    }
  }

  loadingTheFormula = async (dep: number, plan: string, districtId: number) => {
    try {
      const departmentLevel = this.departmentsLevel.find((el) => el.value === dep)?.code ?? null
      const planingStage = this.allStages.find((el) => el.value === plan)?.code ?? null
      if (departmentLevel && planingStage) {
        return await api.calcModelManager.loadingTheFormula(departmentLevel, planingStage, districtId)
      } else {
        return null
      }
    } catch (e) {
      console.log(e)
    }
  }
}
